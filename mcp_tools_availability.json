{"total_tools": 421, "available_tools": 0, "availability_rate": 0.0, "tools": [{"name": "advanced_ai_system", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "setup_inference_runtime", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "optimize_inference", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "setup_frame_slicing", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "configure_ai_quantization", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "setup_custom_backend_dispatching", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "create_natural_language_processing", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "setup_object_recognition", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "configure_sensor_data_classification", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "advanced_ai_quick_setup", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "advanced_ai_system_info", "file": "advanced_ai.py", "status": "function_not_found"}, {"name": "convert_audio_format", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "setup_audio_dsp_time_sync", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "configure_audio_dsp_buffer_size", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "setup_audio_virtualization_runtime", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "create_adaptive_audio_system", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "setup_audio_mixer_snapshot_blending", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "configure_realtime_audio_profiling", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "setup_audio_format_conversion", "file": "advanced_audio.py", "status": "function_not_found"}, {"name": "setup_physics_optimization", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "configure_physics_materials_runtime", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "setup_physics_layers_runtime", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "create_physics_events_system", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "setup_physics_debug_visualization", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "configure_physics_performance_profiling", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "configure_area_masks", "file": "advanced_physics.py", "status": "function_not_found"}, {"name": "generate_procedural_mesh", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "create_procedural_terrain_system", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "setup_procedural_vegetation", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "generate_mesh_from_heightmap", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "create_procedural_building_generator", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "setup_geometry_scripting_runtime", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "generate_l_system_structures", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "create_procedural_road_network", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "optimize_procedural_geometry", "file": "advanced_procedural_generation.py", "status": "function_not_found"}, {"name": "setup_variable_rate_shading", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "configure_shading_rate_image", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "setup_bicubic_lightmap_sampling", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "create_deferred_plus_rendering", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "setup_pipeline_state_tracing", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "configure_shader_variant_stripping", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "setup_3d_water_deformation", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "create_water_caustics_system", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "setup_compute_skinning", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "configure_gpu_instancing_extensions", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "setup_graphics_state_collection", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "configure_read_write_textures", "file": "advanced_rendering.py", "status": "function_not_found"}, {"name": "ai_adaptive_jungle", "file": "ai_adaptive_jungle.py", "status": "function_not_found"}, {"name": "generate_ai_texture", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "generate_ai_sprite", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "generate_ai_material", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "generate_ai_animation", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "generate_ai_sound", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "configure_ai_generator_styles", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "create_texture_variations_ai", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "setup_ai_asset_pipeline", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "generate_material_from_texture_ai", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "refine_ai_generated_assets", "file": "ai_asset_generation.py", "status": "function_not_found"}, {"name": "setup_navmesh_system", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_pathfinding", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_crowd_simulation", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_machine_learning", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_adaptive_difficulty", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "create_ai_perception", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_ai_communication", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_ai_optimization", "file": "ai_runtime.py", "status": "function_not_found"}, {"name": "setup_ik_systems", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "create_facial_animation", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "setup_motion_matching", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "setup_timeline_sequences", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "create_cinemachine_setup", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "create_ragdoll_physics", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "setup_motion_capture", "file": "animation_runtime.py", "status": "function_not_found"}, {"name": "generate_particle_system", "file": "asset_processing.py", "status": "function_not_found"}, {"name": "create_texture_atlas", "file": "asset_processing.py", "status": "function_not_found"}, {"name": "create_mesh_colliders", "file": "asset_processing.py", "status": "function_not_found"}, {"name": "generate_lod_meshes", "file": "asset_processing.py", "status": "function_not_found"}, {"name": "create_uv_mappings", "file": "asset_processing.py", "status": "function_not_found"}, {"name": "optimize_asset_pipeline", "file": "asset_processing.py", "status": "function_not_found"}, {"name": "create_audio_mixer", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_spatial_audio", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_reverb_zones", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_audio_occlusion", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_dynamic_music", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_audio_filters", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_audio_snapshots", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_voice_chat", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_audio_triggers", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_audio_streaming", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_procedural_audio", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_audio_compression", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_audio_effects_chain", "file": "audio_system.py", "status": "function_not_found"}, {"name": "setup_binaural_audio", "file": "audio_system.py", "status": "function_not_found"}, {"name": "create_audio_visualization", "file": "audio_system.py", "status": "function_not_found"}, {"name": "optimize_audio_performance", "file": "audio_system.py", "status": "function_not_found"}, {"name": "generate_timeline_from_events", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "setup_cinemachine_procedural_camera", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "create_dynamic_shot_list", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "sequence_generated_animations_on_timeline", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "add_generated_audio_to_timeline", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "trigger_cinematic_from_gameplay", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "control_post_processing_on_timeline", "file": "automated_cinematics.py", "status": "function_not_found"}, {"name": "manage_behavior_graph", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "generate_behavior_nodes_with_ai", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "create_ai_behavior_branches", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "setup_generative_ai_behavior_graph", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "generate_action_nodes_from_description", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "create_placeholder_behavior_nodes", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "optimize_behavior_tree_with_ai", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "validate_generated_behavior_code", "file": "behavior_ai_generation.py", "status": "function_not_found"}, {"name": "setup_bicubic_lightmap_sampling", "file": "bicubic_lightmap.py", "status": "function_not_found"}, {"name": "configure_lightmap_quality_settings", "file": "bicubic_lightmap.py", "status": "function_not_found"}, {"name": "setup_lightmap_edge_smoothing", "file": "bicubic_lightmap.py", "status": "function_not_found"}, {"name": "create_lightmap_visualization_tools", "file": "bicubic_lightmap.py", "status": "function_not_found"}, {"name": "setup_lightmap_performance_analysis", "file": "bicubic_lightmap.py", "status": "function_not_found"}, {"name": "setup_graphics_override_runtime", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "configure_quality_settings_runtime", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "setup_platform_specific_settings", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "create_dynamic_build_variants", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "setup_runtime_asset_overrides", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "configure_compression_method_runtime", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "setup_shader_variant_collection", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "configure_graphics_api_runtime", "file": "build_profiles_runtime.py", "status": "function_not_found"}, {"name": "champion_fusion_system", "file": "champion_fusion_system.py", "status": "function_not_found"}, {"name": "character_gameplay_system", "file": "character_gameplay_system.py", "status": "function_not_found"}, {"name": "setup_deferred_plus_pipeline", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "configure_forward_plus_transparency", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "setup_deferred_plus_lighting", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "create_deferred_plus_material_system", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "setup_deferred_plus_debug_views", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "configure_deferred_plus_performance", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "setup_deferred_plus_shader_variants", "file": "deferred_plus_rendering.py", "status": "function_not_found"}, {"name": "create_generative_state_machine", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "attach_behaviour_script_to_state", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "setup_procedural_event_system", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "generate_gameplay_rules_engine", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "create_dynamic_skill_tree", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "setup_buff_debuff_system", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "generate_ai_behaviour_tree", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "configure_game_state_manager", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "create_objective_tracker_system", "file": "dynamic_game_systems.py", "status": "function_not_found"}, {"name": "dynamic_realm_system", "file": "dynamic_realm_system.py", "status": "function_not_found"}, {"name": "setup_automated_build_pipeline", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "configure_build_profile_overrides", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "setup_asset_dependency_analysis", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "create_build_step_timing_report", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "setup_script_save_location_handler", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "configure_main_menu_customization", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "setup_version_control_integration", "file": "editor_build_tools.py", "status": "function_not_found"}, {"name": "create_generative_tile_set", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "configure_tile_set_rules", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "generate_tilemap_from_set", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "setup_tile_set_variations", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "create_auto_tiling_system", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "generate_2d_level_layout", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "optimize_generative_tilemap", "file": "generative_2d_world.py", "status": "function_not_found"}, {"name": "analyze_gameplay_data_for_balancing", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "generate_item_database_procedural", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "create_dynamic_shop_inventory", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "setup_procedural_loot_tables", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "simulate_supply_and_demand", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "balance_skill_costs_and_effects", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "generate_enemy_stat_curves", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "create_economy_event_simulator", "file": "generative_game_economy.py", "status": "function_not_found"}, {"name": "setup_graphics_state_tracing", "file": "graphics_state_collection.py", "status": "function_not_found"}, {"name": "configure_pso_prewarming", "file": "graphics_state_collection.py", "status": "function_not_found"}, {"name": "setup_shader_compilation_monitoring", "file": "graphics_state_collection.py", "status": "function_not_found"}, {"name": "create_shader_variant_collection", "file": "graphics_state_collection.py", "status": "function_not_found"}, {"name": "setup_graphics_state_serialization", "file": "graphics_state_collection.py", "status": "function_not_found"}, {"name": "configure_graphics_state_optimization", "file": "graphics_state_collection.py", "status": "function_not_found"}, {"name": "setup_inference_frame_slicing", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "create_inference_quantization_system", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "setup_inference_backend_selection", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "configure_inference_tensor_operations", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "setup_inference_memory_optimization", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "create_inference_model_visualization", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "setup_inference_async_inference", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "configure_inference_onnx_import", "file": "inference_neural_network.py", "status": "function_not_found"}, {"name": "setup_input_system_runtime", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "create_input_action_maps", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "configure_input_processors", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "setup_input_device_management", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "create_input_rebinding_ui", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "setup_input_event_routing", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "configure_input_haptics", "file": "input_system_runtime.py", "status": "function_not_found"}, {"name": "setup_global_illumination", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_light_probes", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_reflection_probes", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_lightmaps", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_realtime_gi", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_volumetric_lighting", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_shadow_cascades", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_light_cookies", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_hdr_pipeline", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_post_processing", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_bloom_effects", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_color_grading", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_depth_of_field", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_motion_blur", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_screen_space_reflections", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_ambient_occlusion", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_fog_effects", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "create_caustics", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "setup_light_shafts", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "optimize_rendering_performance", "file": "lighting_rendering.py", "status": "function_not_found"}, {"name": "lightmap_edge_smoothing", "file": "lightmap_edge_smoothing.py", "status": "function_not_found"}, {"name": "lightmap_performance_analysis", "file": "lightmap_performance_analysis.py", "status": "function_not_found"}, {"name": "lightmap_quality_settings", "file": "lightmap_quality_settings.py", "status": "function_not_found"}, {"name": "lightmap_visualization_tools", "file": "lightmap_visualization_tools.py", "status": "function_not_found"}, {"name": "manage_editor", "file": "manage_editor.py", "status": "function_not_found"}, {"name": "manage_gameobject", "file": "manage_gameobject.py", "status": "function_not_found"}, {"name": "manage_scene", "file": "manage_scene.py", "status": "function_not_found"}, {"name": "manage_script", "file": "manage_script.py", "status": "function_not_found"}, {"name": "configure_multiplayer_netcode", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "setup_unity_gaming_services", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "start_multiplayer_host", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "connect_to_multiplayer_server", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "join_multiplayer_lobby", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "get_multiplayer_status", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "get_connected_players", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "sync_player_data", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "send_multiplayer_message", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "configure_anti_cheat", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "setup_network_culling", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "configure_lag_compensation", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "setup_dedicated_servers", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "monitor_network_performance", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "optimize_bandwidth_usage", "file": "multiplayer_networking.py", "status": "function_not_found"}, {"name": "setup_multiplayer_sessions", "file": "multiplayer_sessions.py", "status": "function_not_found"}, {"name": "configure_distributed_authority", "file": "multiplayer_sessions.py", "status": "function_not_found"}, {"name": "setup_session_matchmaking", "file": "multiplayer_sessions.py", "status": "function_not_found"}, {"name": "create_session_persistence", "file": "multiplayer_sessions.py", "status": "function_not_found"}, {"name": "configure_session_migration", "file": "multiplayer_sessions.py", "status": "function_not_found"}, {"name": "setup_dynamic_navmesh_areas", "file": "navigation_enhanced.py", "status": "function_not_found"}, {"name": "configure_navmesh_costs", "file": "navigation_enhanced.py", "status": "function_not_found"}, {"name": "setup_runtime_obstacle_avoidance", "file": "navigation_enhanced.py", "status": "function_not_found"}, {"name": "create_dynamic_path_modifiers", "file": "navigation_enhanced.py", "status": "function_not_found"}, {"name": "setup_navmesh_links", "file": "navigation_enhanced.py", "status": "function_not_found"}, {"name": "configure_navmesh_area_masks", "file": "navigation_enhanced.py", "status": "function_not_found"}, {"name": "one_click_character_creator", "file": "one_click_character_creator.py", "status": "function_not_found"}, {"name": "batch_character_creator", "file": "one_click_character_creator.py", "status": "function_not_found"}, {"name": "analyze_performance_profile", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "monitor_runtime_performance", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "optimize_memory_usage", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "optimize_rendering_pipeline", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "optimize_assets", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "optimize_build_size", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "setup_performance_budgets", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "configure_adaptive_quality", "file": "performance_profiling.py", "status": "function_not_found"}, {"name": "create_rigidbody_system", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_joint_systems", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_cloth_simulation", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_fluid_simulation", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_destruction_system", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_vehicle_physics", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_rope_physics", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_wind_system", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_gravity_zones", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_collision_layers", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_trigger_systems", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_physics_materials", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_particle_physics", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_soft_body_physics", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_magnetic_fields", "file": "physics_system.py", "status": "function_not_found"}, {"name": "setup_buoyancy_system", "file": "physics_system.py", "status": "function_not_found"}, {"name": "create_physics_constraints", "file": "physics_system.py", "status": "function_not_found"}, {"name": "optimize_physics_performance", "file": "physics_system.py", "status": "function_not_found"}, {"name": "procedural_animation_generation", "file": "procedural_animation_generation.py", "status": "function_not_found"}, {"name": "procedural_body_part_generation", "file": "procedural_body_part_generation.py", "status": "function_not_found"}, {"name": "generate_quest_state_machine", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "create_quest_objective_generator", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "setup_inference_dialogue_generator", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "generate_branching_dialogue_tree", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "create_narrative_event_chain", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "setup_dynamic_lore_system", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "generate_npc_backstory", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "create_quest_reward_generator", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "link_quests_to_gameplay_events", "file": "procedural_narrative.py", "status": "function_not_found"}, {"name": "procedural_skeleton_generation", "file": "procedural_skeleton_generation.py", "status": "function_not_found"}, {"name": "procedural_texture_generation", "file": "procedural_texture_generation.py", "status": "function_not_found"}, {"name": "setup_project_auditor_integration", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "configure_asset_usage_analysis", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "setup_shader_variant_reporting", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "create_memory_profiling_report", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "setup_domain_reload_monitoring", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "configure_build_size_analysis", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "setup_code_analysis_runtime", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "create_performance_recommendations", "file": "project_auditor_enhanced.py", "status": "function_not_found"}, {"name": "setup_project_auditor", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "configure_runtime_diagnostics", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "setup_domain_reload_analysis", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "create_asset_usage_report", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "setup_shader_variant_analysis", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "configure_memory_profiling", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "configure_graphics_state_optimization", "file": "project_auditor_runtime.py", "status": "function_not_found"}, {"name": "setup_project_dependencies", "file": "project_code_operations.py", "status": "function_not_found"}, {"name": "configure_build_pipeline", "file": "project_code_operations.py", "status": "function_not_found"}, {"name": "validate_project_integrity", "file": "project_code_operations.py", "status": "function_not_found"}, {"name": "optimize_existing_code", "file": "project_code_operations.py", "status": "function_not_found"}, {"name": "refactor_code_structure", "file": "project_code_operations.py", "status": "function_not_found"}, {"name": "update_acceleration_structure", "file": "raytracing_operations.py", "status": "function_not_found"}, {"name": "add_raytracing_instances", "file": "raytracing_operations.py", "status": "function_not_found"}, {"name": "remove_raytracing_instance", "file": "raytracing_operations.py", "status": "function_not_found"}, {"name": "cull_raytracing_instances", "file": "raytracing_operations.py", "status": "function_not_found"}, {"name": "configure_raytracing_pipeline", "file": "raytracing_operations.py", "status": "function_not_found"}, {"name": "update_raytracing_geometry", "file": "raytracing_operations.py", "status": "function_not_found"}, {"name": "read_console", "file": "read_console.py", "status": "function_not_found"}, {"name": "session_matchmaking", "file": "session_matchmaking.py", "status": "function_not_found"}, {"name": "session_migration", "file": "session_migration.py", "status": "function_not_found"}, {"name": "session_persistence", "file": "session_persistence.py", "status": "function_not_found"}, {"name": "create_terrain", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "paint_terrain_textures", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "create_terrain_trees", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "setup_terrain_grass", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "create_terrain_details", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "setup_terrain_collision", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "create_water_system", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "setup_weather_system", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "create_day_night_cycle", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "setup_environment_zones", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "create_biome_system", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "setup_procedural_caves", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "create_landmark_generation", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "optimize_terrain_performance", "file": "terrain_environment.py", "status": "function_not_found"}, {"name": "setup_tilemap_generation", "file": "toolkit_2d_runtime.py", "status": "function_not_found"}, {"name": "configure_tile_sets", "file": "toolkit_2d_runtime.py", "status": "function_not_found"}, {"name": "setup_sprite_atlas_runtime", "file": "toolkit_2d_runtime.py", "status": "function_not_found"}, {"name": "create_2d_physics_system", "file": "toolkit_2d_runtime.py", "status": "function_not_found"}, {"name": "setup_2d_animation_runtime", "file": "toolkit_2d_runtime.py", "status": "function_not_found"}, {"name": "setup_ui_toolkit_runtime", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "configure_mask64field_controls", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "setup_ui_profiler_markers", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "create_ui_data_binding", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "setup_ui_runtime_events", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "configure_ui_renderer_optimizations", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "setup_ui_toolkit_animations", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "create_dynamic_ui_elements", "file": "ui_toolkit_runtime.py", "status": "function_not_found"}, {"name": "setup_ai_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "generate_code_with_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "troubleshoot_with_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "automate_tasks_with_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "scene_editing_with_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "asset_management_with_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "project_analysis_with_assistant", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "assistant_workflow_automation", "file": "unity_ai_assistant.py", "status": "function_not_found"}, {"name": "setup_unity_cloud_ai_project", "file": "unity_cloud_ai.py", "status": "function_not_found"}, {"name": "manage_ai_points_system", "file": "unity_cloud_ai.py", "status": "function_not_found"}, {"name": "configure_ai_cloud_services", "file": "unity_cloud_ai.py", "status": "function_not_found"}, {"name": "setup_ai_asset_cloud_sync", "file": "unity_cloud_ai.py", "status": "function_not_found"}, {"name": "monitor_ai_usage_analytics", "file": "unity_cloud_ai.py", "status": "function_not_found"}, {"name": "configure_ai_billing_optimization", "file": "unity_cloud_ai.py", "status": "function_not_found"}, {"name": "unity_cloud_testing", "file": "unity_cloud_testing.py", "status": "function_not_found"}, {"name": "setup_unity_ai", "file": "unity_testing.py", "status": "function_not_found"}, {"name": "run_unity_test_suite", "file": "unity_testing.py", "status": "function_not_found"}, {"name": "configure_cloud_testing", "file": "unity_testing.py", "status": "function_not_found"}, {"name": "unity_test_runner", "file": "unity_test_runner.py", "status": "function_not_found"}, {"name": "setup_vrs_custom_passes", "file": "variable_rate_shading.py", "status": "function_not_found"}, {"name": "configure_shading_rate_image", "file": "variable_rate_shading.py", "status": "function_not_found"}, {"name": "setup_vrs_performance_scaling", "file": "variable_rate_shading.py", "status": "function_not_found"}, {"name": "create_vrs_quality_regions", "file": "variable_rate_shading.py", "status": "function_not_found"}, {"name": "setup_vrs_api_integration", "file": "variable_rate_shading.py", "status": "function_not_found"}, {"name": "configure_vrs_debug_visualization", "file": "variable_rate_shading.py", "status": "function_not_found"}, {"name": "create_particle_systems", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_vfx_graph", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "create_fire_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_water_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "create_explosion_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_magic_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "create_weather_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_environmental_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "create_ui_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_screen_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "create_trail_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_distortion_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "create_hologram_effects", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "optimize_vfx_performance", "file": "vfx_particles.py", "status": "function_not_found"}, {"name": "setup_vfx_graph_shader_binding", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "configure_vfx_gpu_instancing", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "setup_vfx_runtime_recompilation", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "create_vfx_parameter_controller", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "setup_vfx_parallelization_tuning", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "configure_shader_graph_vfx_support", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "setup_vfx_particle_data_layout", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "create_vfx_garbage_collection_monitor", "file": "vfx_shader_graph_control.py", "status": "function_not_found"}, {"name": "setup_3d_water_deformation", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "configure_water_rolling_waves", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "setup_water_caustics_buffer", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "create_water_foam_system", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "setup_water_flow_maps", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "configure_water_mask_system", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "setup_water_query_api", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "create_water_line_transition", "file": "water_system_runtime.py", "status": "function_not_found"}, {"name": "setup_webgpu_compute_shaders", "file": "webgpu_runtime.py", "status": "function_not_found"}, {"name": "configure_webgpu_indirect_rendering", "file": "webgpu_runtime.py", "status": "function_not_found"}, {"name": "setup_webgpu_gpu_skinning", "file": "webgpu_runtime.py", "status": "function_not_found"}, {"name": "create_webgpu_vfx_system", "file": "webgpu_runtime.py", "status": "function_not_found"}, {"name": "setup_webgpu_compatibility_detection", "file": "webgpu_runtime.py", "status": "function_not_found"}, {"name": "create_faction_simulation_system", "file": "world_simulation_systems.py", "status": "function_not_found"}, {"name": "setup_dynamic_event_spawner", "file": "world_simulation_systems.py", "status": "function_not_found"}, {"name": "simulate_npc_daily_routines", "file": "world_simulation_systems.py", "status": "function_not_found"}, {"name": "create_simulated_ecosystem", "file": "world_simulation_systems.py", "status": "function_not_found"}, {"name": "setup_world_state_change_system", "file": "world_simulation_systems.py", "status": "function_not_found"}, {"name": "generate_dynamic_points_of_interest", "file": "world_simulation_systems.py", "status": "function_not_found"}, {"name": "setup_xr_toolkit", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_vr_player_rig", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_hand_tracking", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_ar_foundation_setup", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_eye_tracking", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_spatial_anchors", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_passthrough_ar", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_mixed_reality_setup", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_haptic_feedback", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_xr_ui_system", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_room_scale_tracking", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_teleportation_system", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_comfort_settings", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_xr_input_system", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_cross_platform_xr", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_ar_occlusion", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "setup_xr_performance_optimization", "file": "xr_runtime.py", "status": "function_not_found"}, {"name": "create_xr_analytics", "file": "xr_runtime.py", "status": "function_not_found"}], "status_summary": {"function_not_found": 421}}