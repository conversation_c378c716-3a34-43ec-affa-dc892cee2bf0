#!/usr/bin/env python3
"""
Script para verificar handlers ausentes entre Python e C#
"""
import json
import re
import os

def extract_python_commands():
    """Extrai comandos dos arquivos Python."""
    commands = set()
    
    with open('mcp_tools_inventory.json', 'r', encoding='utf-8') as f:
        inventory = json.load(f)
    
    for tool in inventory['tools']:
        # Converter nome da função para nome do handler
        tool_name = tool['name']
        # Converter snake_case para PascalCase
        handler_name = 'Handle' + ''.join(word.capitalize() for word in tool_name.split('_'))
        commands.add(handler_name)
    
    return commands

def extract_csharp_handlers():
    """Extrai handlers registrados no C#."""
    handlers = set()
    
    with open('UnityMcpBridge/Editor/Tools/CommandRegistry.cs', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Procurar por padrões como { "HandleXxx", ... }
    pattern = r'{\s*"(Handle[^"]+)"'
    matches = re.findall(pattern, content)
    
    for match in matches:
        handlers.add(match)
    
    return handlers

def find_missing_handlers():
    """Encontra handlers ausentes."""
    python_commands = extract_python_commands()
    csharp_handlers = extract_csharp_handlers()
    
    missing_in_csharp = python_commands - csharp_handlers
    missing_in_python = csharp_handlers - python_commands
    
    print("🔍 ANÁLISE DE HANDLERS MCP")
    print("=" * 50)
    print(f"📊 Comandos Python: {len(python_commands)}")
    print(f"📊 Handlers C#: {len(csharp_handlers)}")
    print(f"❌ Ausentes no C#: {len(missing_in_csharp)}")
    print(f"❌ Ausentes no Python: {len(missing_in_python)}")
    
    if missing_in_csharp:
        print("\n❌ HANDLERS AUSENTES NO C#:")
        for handler in sorted(missing_in_csharp):
            print(f"  - {handler}")
    
    if missing_in_python:
        print("\n❌ COMANDOS AUSENTES NO PYTHON:")
        for handler in sorted(missing_in_python):
            print(f"  - {handler}")
    
    # Salvar relatório
    report = {
        'python_commands_count': len(python_commands),
        'csharp_handlers_count': len(csharp_handlers),
        'missing_in_csharp': list(sorted(missing_in_csharp)),
        'missing_in_python': list(sorted(missing_in_python)),
        'python_commands': list(sorted(python_commands)),
        'csharp_handlers': list(sorted(csharp_handlers))
    }
    
    with open('handlers_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Relatório salvo em: handlers_analysis.json")
    
    return missing_in_csharp, missing_in_python

if __name__ == "__main__":
    find_missing_handlers()
