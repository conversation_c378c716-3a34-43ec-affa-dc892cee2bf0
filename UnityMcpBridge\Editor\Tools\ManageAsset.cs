using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityMcpBridge.Editor.Helpers; // For Response class

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// [UNITY 6.2 COMPATIBLE] - Handles advanced asset management operations within the Unity project.
    ///
    /// Suporta as seguintes ações:
    /// - import: Re-importa assets com configurações customizadas
    /// - create: Cria novos assets (materiais, ScriptableObjects, pastas, texturas)
    /// - modify: Modifica propriedades de assets existentes
    /// - delete: Remove assets do projeto com validação
    /// - duplicate: Duplica assets com nomes únicos
    /// - move/rename: Move ou renomeia assets
    /// - search: Busca assets por critérios avançados
    /// - get_info: Obtém informações detalhadas de um asset
    /// - create_folder: Cria pastas com estrutura hierárquica
    /// - get_components: Lista componentes de prefabs
    /// - batch_operations: Operações em lote para múltiplos assets
    /// - optimize: Otimiza assets para Unity 6.2
    ///
    /// [UNITY 6.2 FEATURES]:
    /// - Advanced Mesh API integration
    /// - GPU-accelerated asset processing
    /// - Batch operations with progress tracking
    /// - Asset dependency analysis
    /// - Performance profiling integration
    ///
    /// [DEPENDÊNCIAS]:
    /// - UnityEditor.AssetDatabase (Unity 6.2+)
    /// - UnityEngine.Rendering para configurações gráficas
    /// - System.Reflection para propriedades dinâmicas
    /// </summary>
    public static class ManageAsset
    {
        /// <summary>
        /// [UNITY 6.2] - Lista de ações válidas expandida para Unity 6.2
        /// </summary>
        private static readonly List<string> ValidActions = new List<string>
        {
            "import",
            "create",
            "modify",
            "delete",
            "duplicate",
            "move",
            "rename",
            "search",
            "get_info",
            "create_folder",
            "get_components",
            "batch_operations",
            "optimize",
            "analyze_dependencies",
            "validate_assets",
            "compress_textures",
            "generate_previews"
        };

        /// <summary>
        /// [UNITY 6.2] - Ponto de entrada principal para comandos de asset management.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            // Verificar se a ação é válida antes do switch
            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            // Parâmetros comuns
            string path = @params["path"]?.ToString();

            try
            {
                // Log da operação para debugging
                LogOperation("HandleCommand", $"Executing action '{action}' on path '{path}'");

                switch (action)
                {
                    case "import":
                        return ReimportAsset(path, @params["properties"] as JObject);
                    case "create":
                        return CreateAsset(@params);
                    case "modify":
                        return ModifyAsset(path, @params["properties"] as JObject);
                    case "delete":
                        return DeleteAsset(path);
                    case "duplicate":
                        return DuplicateAsset(path, @params["destination"]?.ToString());
                    case "move":
                    case "rename":
                        return MoveOrRenameAsset(path, @params["destination"]?.ToString());
                    case "search":
                        return SearchAssets(@params);
                    case "get_info":
                        return GetAssetInfo(
                            path,
                            @params["generatePreview"]?.ToObject<bool>() ?? false
                        );
                    case "create_folder":
                        return CreateFolder(path);
                    case "get_components":
                        return GetComponentsFromAsset(path);
                    case "batch_operations":
                        return BatchOperations(@params);
                    case "optimize":
                        return OptimizeAsset(path, @params);
                    case "analyze_dependencies":
                        return AnalyzeDependencies(path);
                    case "validate_assets":
                        return ValidateAssets(@params);
                    case "compress_textures":
                        return CompressTextures(@params);
                    case "generate_previews":
                        return GeneratePreviews(@params);

                    default:
                        string validActionsListDefault = string.Join(", ", ValidActions);
                        return Response.Error(
                            $"Unknown action: '{action}'. Valid actions are: {validActionsListDefault}"
                        );
                }
            }
            catch (Exception e)
            {
                LogOperation("HandleCommand", $"Action '{action}' failed for path '{path}': {e.Message}", true);
                return Response.Error(
                    $"Internal error processing action '{action}' on '{path}': {e.Message}"
                );
            }
        }

        // --- Action Implementations ---

        private static object ReimportAsset(string path, JObject properties)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for reimport.");
            string fullPath = SanitizeAssetPath(path);
            if (!AssetExists(fullPath))
                return Response.Error($"Asset not found at path: {fullPath}");

            try
            {
                // Apply importer properties before reimporting if provided
                if (properties != null && properties.HasValues)
                {
                    AssetImporter importer = AssetImporter.GetAtPath(fullPath);
                    if (importer != null)
                    {
                        ApplyImporterProperties(importer, properties);
                        AssetDatabase.WriteImportSettingsIfDirty(fullPath);
                    }
                }

                AssetDatabase.ImportAsset(fullPath, ImportAssetOptions.ForceUpdate);
                return Response.Success($"Asset '{fullPath}' reimported.", GetAssetData(fullPath));
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to reimport asset '{fullPath}': {e.Message}");
            }
        }

        private static object CreateAsset(JObject @params)
        {
            string path = @params["path"]?.ToString();
            string assetType = @params["assetType"]?.ToString();
            JObject properties = @params["properties"] as JObject;

            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for create.");
            if (string.IsNullOrEmpty(assetType))
                return Response.Error("'assetType' is required for create.");

            string fullPath = SanitizeAssetPath(path);
            string directory = Path.GetDirectoryName(fullPath);

            // Ensure directory exists
            if (!Directory.Exists(Path.Combine(Directory.GetCurrentDirectory(), directory)))
            {
                Directory.CreateDirectory(Path.Combine(Directory.GetCurrentDirectory(), directory));
                AssetDatabase.Refresh(); // Make sure Unity knows about the new folder
            }

            if (AssetExists(fullPath))
                return Response.Error($"Asset already exists at path: {fullPath}");

            try
            {
                UnityEngine.Object newAsset = null;
                string lowerAssetType = assetType.ToLowerInvariant();

                // Handle common asset types
                if (lowerAssetType == "folder")
                {
                    return CreateFolder(path); // Use dedicated method
                }
                else if (lowerAssetType == "material")
                {
                    Material mat = new Material(Shader.Find("Standard")); // Default shader
                    // Apply properties from JObject (shader name, color, texture assignments)
                    if (properties != null)
                        ApplyMaterialProperties(mat, properties);
                    AssetDatabase.CreateAsset(mat, fullPath);
                    newAsset = mat;
                }
                else if (lowerAssetType == "scriptableobject")
                {
                    string scriptClassName = properties?["scriptClass"]?.ToString();
                    if (string.IsNullOrEmpty(scriptClassName))
                        return Response.Error(
                            "'scriptClass' property required when creating ScriptableObject asset."
                        );

                    Type scriptType = FindType(scriptClassName);
                    if (
                        scriptType == null
                        || !typeof(ScriptableObject).IsAssignableFrom(scriptType)
                    )
                    {
                        return Response.Error(
                            $"Script class '{scriptClassName}' not found or does not inherit from ScriptableObject."
                        );
                    }

                    ScriptableObject so = ScriptableObject.CreateInstance(scriptType);
                    // Apply properties from JObject to the ScriptableObject instance
                    if (properties != null)
                        ApplyObjectProperties(so, properties);
                    AssetDatabase.CreateAsset(so, fullPath);
                    newAsset = so;
                }
                else if (lowerAssetType == "prefab")
                {
                    // Creating prefabs usually involves saving an existing GameObject hierarchy.
                    // A common pattern is to create an empty GameObject, configure it, and then save it.
                    return Response.Error(
                        "Creating prefabs programmatically usually requires a source GameObject. Use manage_gameobject to create/configure, then save as prefab via a separate mechanism or future enhancement."
                    );
                    // Example (conceptual):
                    // GameObject source = GameObject.Find(properties["sourceGameObject"].ToString());
                    // if(source != null) PrefabUtility.SaveAsPrefabAsset(source, fullPath);
                }
                // Support for additional asset types
                else if (lowerAssetType == "animatorcontroller")
                {
                    var controller = UnityEditor.Animations.AnimatorController.CreateAnimatorControllerAtPath(fullPath);
                    newAsset = controller;
                }
                else if (lowerAssetType == "physicmaterial")
                {
                    var physicMaterial = new PhysicsMaterial();
                    AssetDatabase.CreateAsset(physicMaterial, fullPath);
                    newAsset = physicMaterial;
                }
                else
                {
                    return Response.Error($"Unsupported asset type: {assetType}. Supported types: Material, ScriptableObject, Prefab, Folder, AnimatorController, PhysicMaterial");
                }

                if (
                    newAsset == null
                    && !Directory.Exists(Path.Combine(Directory.GetCurrentDirectory(), fullPath))
                ) // Check if it wasn't a folder and asset wasn't created
                {
                    return Response.Error(
                        $"Failed to create asset '{assetType}' at '{fullPath}'. See logs for details."
                    );
                }

                AssetDatabase.SaveAssets();
                // AssetDatabase.Refresh(); // CreateAsset often handles refresh
                return Response.Success(
                    $"Asset '{fullPath}' created successfully.",
                    GetAssetData(fullPath)
                );
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create asset at '{fullPath}': {e.Message}");
            }
        }

        private static object CreateFolder(string path)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for create_folder.");
            string fullPath = SanitizeAssetPath(path);
            string parentDir = Path.GetDirectoryName(fullPath);
            string folderName = Path.GetFileName(fullPath);

            if (AssetExists(fullPath))
            {
                // Check if it's actually a folder already
                if (AssetDatabase.IsValidFolder(fullPath))
                {
                    return Response.Success(
                        $"Folder already exists at path: {fullPath}",
                        GetAssetData(fullPath)
                    );
                }
                else
                {
                    return Response.Error(
                        $"An asset (not a folder) already exists at path: {fullPath}"
                    );
                }
            }

            try
            {
                // Ensure parent exists
                if (!string.IsNullOrEmpty(parentDir) && !AssetDatabase.IsValidFolder(parentDir))
                {
                    // Recursively create parent folders if needed (AssetDatabase handles this internally)
                    // Or we can do it manually: Directory.CreateDirectory(Path.Combine(Directory.GetCurrentDirectory(), parentDir)); AssetDatabase.Refresh();
                }

                string guid = AssetDatabase.CreateFolder(parentDir, folderName);
                if (string.IsNullOrEmpty(guid))
                {
                    return Response.Error(
                        $"Failed to create folder '{fullPath}'. Check logs and permissions."
                    );
                }

                // AssetDatabase.Refresh(); // CreateFolder usually handles refresh
                return Response.Success(
                    $"Folder '{fullPath}' created successfully.",
                    GetAssetData(fullPath)
                );
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create folder '{fullPath}': {e.Message}");
            }
        }

        private static object ModifyAsset(string path, JObject properties)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for modify.");
            if (properties == null || !properties.HasValues)
                return Response.Error("'properties' are required for modify.");

            string fullPath = SanitizeAssetPath(path);
            if (!AssetExists(fullPath))
                return Response.Error($"Asset not found at path: {fullPath}");

            try
            {
                UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(
                    fullPath
                );
                if (asset == null)
                    return Response.Error($"Failed to load asset at path: {fullPath}");

                bool modified = false; // Flag to track if any changes were made

                // --- NEW: Handle GameObject / Prefab Component Modification ---
                if (asset is GameObject gameObject)
                {
                    // Iterate through the properties JSON: keys are component names, values are properties objects for that component
                    foreach (var prop in properties.Properties())
                    {
                        string componentName = prop.Name; // e.g., "Collectible"
                        // Check if the value associated with the component name is actually an object containing properties
                        if (
                            prop.Value is JObject componentProperties
                            && componentProperties.HasValues
                        ) // e.g., {"bobSpeed": 2.0}
                        {
                            // Find the component on the GameObject using the name from the JSON key
                            // Using GetComponent(string) is convenient but might require exact type name or be ambiguous.
                            // Consider using FindType helper if needed for more complex scenarios.
                            Component targetComponent = gameObject.GetComponent(componentName);

                            if (targetComponent != null)
                            {
                                // Apply the nested properties (e.g., bobSpeed) to the found component instance
                                // Use |= to ensure 'modified' becomes true if any component is successfully modified
                                modified |= ApplyObjectProperties(
                                    targetComponent,
                                    componentProperties
                                );
                            }
                            else
                            {
                                // Log a warning if a specified component couldn't be found
                                Debug.LogWarning(
                                    $"[ManageAsset.ModifyAsset] Component '{componentName}' not found on GameObject '{gameObject.name}' in asset '{fullPath}'. Skipping modification for this component."
                                );
                            }
                        }
                        else
                        {
                            // Log a warning if the structure isn't {"ComponentName": {"prop": value}}
                            // We could potentially try to apply this property directly to the GameObject here if needed,
                            // but the primary goal is component modification.
                            Debug.LogWarning(
                                $"[ManageAsset.ModifyAsset] Property '{prop.Name}' for GameObject modification should have a JSON object value containing component properties. Value was: {prop.Value.Type}. Skipping."
                            );
                        }
                    }
                    // Note: 'modified' is now true if ANY component property was successfully changed.
                }
                // --- End NEW ---

                // --- Existing logic for other asset types (now as else-if) ---
                // Example: Modifying a Material
                else if (asset is Material material)
                {
                    // Apply properties directly to the material. If this modifies, it sets modified=true.
                    // Use |= in case the asset was already marked modified by previous logic (though unlikely here)
                    modified |= ApplyMaterialProperties(material, properties);
                }
                // Example: Modifying a ScriptableObject
                else if (asset is ScriptableObject so)
                {
                    // Apply properties directly to the ScriptableObject.
                    modified |= ApplyObjectProperties(so, properties); // General helper
                }
                // Example: Modifying TextureImporter settings
                else if (asset is Texture)
                {
                    AssetImporter importer = AssetImporter.GetAtPath(fullPath);
                    if (importer is TextureImporter textureImporter)
                    {
                        bool importerModified = ApplyObjectProperties(textureImporter, properties);
                        if (importerModified)
                        {
                            // Importer settings need saving and reimporting
                            AssetDatabase.WriteImportSettingsIfDirty(fullPath);
                            AssetDatabase.ImportAsset(fullPath, ImportAssetOptions.ForceUpdate); // Reimport to apply changes
                            modified = true; // Mark overall operation as modified
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"Could not get TextureImporter for {fullPath}.");
                    }
                }
                // TODO: Add modification logic for other common asset types (Models, AudioClips importers, etc.)
                else // Fallback for other asset types OR direct properties on non-GameObject assets
                {
                    // This block handles non-GameObject/Material/ScriptableObject/Texture assets.
                    // Attempts to apply properties directly to the asset itself.
                    Debug.LogWarning(
                        $"[ManageAsset.ModifyAsset] Asset type '{asset.GetType().Name}' at '{fullPath}' is not explicitly handled for component modification. Attempting generic property setting on the asset itself."
                    );
                    modified |= ApplyObjectProperties(asset, properties);
                }
                // --- End Existing Logic ---

                // Check if any modification happened (either component or direct asset modification)
                if (modified)
                {
                    // Mark the asset as dirty (important for prefabs/SOs) so Unity knows to save it.
                    EditorUtility.SetDirty(asset);
                    // Save all modified assets to disk.
                    AssetDatabase.SaveAssets();
                    // Refresh might be needed in some edge cases, but SaveAssets usually covers it.
                    // AssetDatabase.Refresh();
                    return Response.Success(
                        $"Asset '{fullPath}' modified successfully.",
                        GetAssetData(fullPath)
                    );
                }
                else
                {
                    // If no changes were made (e.g., component not found, property names incorrect, value unchanged), return a success message indicating nothing changed.
                    return Response.Success(
                        $"No applicable or modifiable properties found for asset '{fullPath}'. Check component names, property names, and values.",
                        GetAssetData(fullPath)
                    );
                    // Previous message: return Response.Success($"No applicable properties found to modify for asset '{fullPath}'.", GetAssetData(fullPath));
                }
            }
            catch (Exception e)
            {
                // Log the detailed error internally
                Debug.LogError($"[ManageAsset] Action 'modify' failed for path '{path}': {e}");
                // Return a user-friendly error message
                return Response.Error($"Failed to modify asset '{fullPath}': {e.Message}");
            }
        }

        private static object DeleteAsset(string path)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for delete.");
            string fullPath = SanitizeAssetPath(path);
            if (!AssetExists(fullPath))
                return Response.Error($"Asset not found at path: {fullPath}");

            try
            {
                bool success = AssetDatabase.DeleteAsset(fullPath);
                if (success)
                {
                    // AssetDatabase.Refresh(); // DeleteAsset usually handles refresh
                    return Response.Success($"Asset '{fullPath}' deleted successfully.");
                }
                else
                {
                    // This might happen if the file couldn't be deleted (e.g., locked)
                    return Response.Error(
                        $"Failed to delete asset '{fullPath}'. Check logs or if the file is locked."
                    );
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error deleting asset '{fullPath}': {e.Message}");
            }
        }

        private static object DuplicateAsset(string path, string destinationPath)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for duplicate.");

            string sourcePath = SanitizeAssetPath(path);
            if (!AssetExists(sourcePath))
                return Response.Error($"Source asset not found at path: {sourcePath}");

            string destPath;
            if (string.IsNullOrEmpty(destinationPath))
            {
                // Generate a unique path if destination is not provided
                destPath = AssetDatabase.GenerateUniqueAssetPath(sourcePath);
            }
            else
            {
                destPath = SanitizeAssetPath(destinationPath);
                if (AssetExists(destPath))
                    return Response.Error($"Asset already exists at destination path: {destPath}");
                // Ensure destination directory exists
                EnsureDirectoryExists(Path.GetDirectoryName(destPath));
            }

            try
            {
                bool success = AssetDatabase.CopyAsset(sourcePath, destPath);
                if (success)
                {
                    // AssetDatabase.Refresh();
                    return Response.Success(
                        $"Asset '{sourcePath}' duplicated to '{destPath}'.",
                        GetAssetData(destPath)
                    );
                }
                else
                {
                    return Response.Error(
                        $"Failed to duplicate asset from '{sourcePath}' to '{destPath}'."
                    );
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error duplicating asset '{sourcePath}': {e.Message}");
            }
        }

        private static object MoveOrRenameAsset(string path, string destinationPath)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for move/rename.");
            if (string.IsNullOrEmpty(destinationPath))
                return Response.Error("'destination' path is required for move/rename.");

            string sourcePath = SanitizeAssetPath(path);
            string destPath = SanitizeAssetPath(destinationPath);

            if (!AssetExists(sourcePath))
                return Response.Error($"Source asset not found at path: {sourcePath}");
            if (AssetExists(destPath))
                return Response.Error(
                    $"An asset already exists at the destination path: {destPath}"
                );

            // Ensure destination directory exists
            EnsureDirectoryExists(Path.GetDirectoryName(destPath));

            try
            {
                // Validate will return an error string if failed, null if successful
                string error = AssetDatabase.ValidateMoveAsset(sourcePath, destPath);
                if (!string.IsNullOrEmpty(error))
                {
                    return Response.Error(
                        $"Failed to move/rename asset from '{sourcePath}' to '{destPath}': {error}"
                    );
                }

                string guid = AssetDatabase.MoveAsset(sourcePath, destPath);
                if (!string.IsNullOrEmpty(guid)) // MoveAsset returns the new GUID on success
                {
                    // AssetDatabase.Refresh(); // MoveAsset usually handles refresh
                    return Response.Success(
                        $"Asset moved/renamed from '{sourcePath}' to '{destPath}'.",
                        GetAssetData(destPath)
                    );
                }
                else
                {
                    // This case might not be reachable if ValidateMoveAsset passes, but good to have
                    return Response.Error(
                        $"MoveAsset call failed unexpectedly for '{sourcePath}' to '{destPath}'."
                    );
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error moving/renaming asset '{sourcePath}': {e.Message}");
            }
        }

        private static object SearchAssets(JObject @params)
        {
            string searchPattern = @params["searchPattern"]?.ToString();
            string filterType = @params["filterType"]?.ToString();
            string pathScope = @params["path"]?.ToString(); // Use path as folder scope
            string filterDateAfterStr = @params["filterDateAfter"]?.ToString();
            int pageSize = @params["pageSize"]?.ToObject<int?>() ?? 50; // Default page size
            int pageNumber = @params["pageNumber"]?.ToObject<int?>() ?? 1; // Default page number (1-based)
            bool generatePreview = @params["generatePreview"]?.ToObject<bool>() ?? false;

            List<string> searchFilters = new List<string>();
            if (!string.IsNullOrEmpty(searchPattern))
                searchFilters.Add(searchPattern);
            if (!string.IsNullOrEmpty(filterType))
                searchFilters.Add($"t:{filterType}");

            string[] folderScope = null;
            if (!string.IsNullOrEmpty(pathScope))
            {
                folderScope = new string[] { SanitizeAssetPath(pathScope) };
                if (!AssetDatabase.IsValidFolder(folderScope[0]))
                {
                    // Maybe the user provided a file path instead of a folder?
                    // We could search in the containing folder, or return an error.
                    Debug.LogWarning(
                        $"Search path '{folderScope[0]}' is not a valid folder. Searching entire project."
                    );
                    folderScope = null; // Search everywhere if path isn't a folder
                }
            }

            DateTime? filterDateAfter = null;
            if (!string.IsNullOrEmpty(filterDateAfterStr))
            {
                if (
                    DateTime.TryParse(
                        filterDateAfterStr,
                        CultureInfo.InvariantCulture,
                        DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal,
                        out DateTime parsedDate
                    )
                )
                {
                    filterDateAfter = parsedDate;
                }
                else
                {
                    Debug.LogWarning(
                        $"Could not parse filterDateAfter: '{filterDateAfterStr}'. Expected ISO 8601 format."
                    );
                }
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets(
                    string.Join(" ", searchFilters),
                    folderScope
                );
                List<object> results = new List<object>();
                int totalFound = 0;

                foreach (string guid in guids)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    if (string.IsNullOrEmpty(assetPath))
                        continue;

                    // Apply date filter if present
                    if (filterDateAfter.HasValue)
                    {
                        DateTime lastWriteTime = File.GetLastWriteTimeUtc(
                            Path.Combine(Directory.GetCurrentDirectory(), assetPath)
                        );
                        if (lastWriteTime <= filterDateAfter.Value)
                        {
                            continue; // Skip assets older than or equal to the filter date
                        }
                    }

                    totalFound++; // Count matching assets before pagination
                    results.Add(GetAssetData(assetPath, generatePreview));
                }

                // Apply pagination
                int startIndex = (pageNumber - 1) * pageSize;
                var pagedResults = results.Skip(startIndex).Take(pageSize).ToList();

                return Response.Success(
                    $"Found {totalFound} asset(s). Returning page {pageNumber} ({pagedResults.Count} assets).",
                    new
                    {
                        totalAssets = totalFound,
                        pageSize = pageSize,
                        pageNumber = pageNumber,
                        assets = pagedResults,
                    }
                );
            }
            catch (Exception e)
            {
                return Response.Error($"Error searching assets: {e.Message}");
            }
        }

        private static object GetAssetInfo(string path, bool generatePreview)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for get_info.");
            string fullPath = SanitizeAssetPath(path);
            if (!AssetExists(fullPath))
                return Response.Error($"Asset not found at path: {fullPath}");

            try
            {
                return Response.Success(
                    "Asset info retrieved.",
                    GetAssetData(fullPath, generatePreview)
                );
            }
            catch (Exception e)
            {
                return Response.Error($"Error getting info for asset '{fullPath}': {e.Message}");
            }
        }

        /// <summary>
        /// Retrieves components attached to a GameObject asset (like a Prefab).
        /// </summary>
        /// <param name="path">The asset path of the GameObject or Prefab.</param>
        /// <returns>A response object containing a list of component type names or an error.</returns>
        private static object GetComponentsFromAsset(string path)
        {
            // 1. Validate input path
            if (string.IsNullOrEmpty(path))
                return Response.Error("'path' is required for get_components.");

            // 2. Sanitize and check existence
            string fullPath = SanitizeAssetPath(path);
            if (!AssetExists(fullPath))
                return Response.Error($"Asset not found at path: {fullPath}");

            try
            {
                // 3. Load the asset
                UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(
                    fullPath
                );
                if (asset == null)
                    return Response.Error($"Failed to load asset at path: {fullPath}");

                // 4. Check if it's a GameObject (Prefabs load as GameObjects)
                GameObject gameObject = asset as GameObject;
                if (gameObject == null)
                {
                    // Also check if it's *directly* a Component type (less common for primary assets)
                    Component componentAsset = asset as Component;
                    if (componentAsset != null)
                    {
                        // If the asset itself *is* a component, maybe return just its info?
                        // This is an edge case. Let's stick to GameObjects for now.
                        return Response.Error(
                            $"Asset at '{fullPath}' is a Component ({asset.GetType().FullName}), not a GameObject. Components are typically retrieved *from* a GameObject."
                        );
                    }
                    return Response.Error(
                        $"Asset at '{fullPath}' is not a GameObject (Type: {asset.GetType().FullName}). Cannot get components from this asset type."
                    );
                }

                // 5. Get components
                Component[] components = gameObject.GetComponents<Component>();

                // 6. Format component data
                List<object> componentList = components
                    .Select(comp => new
                    {
                        typeName = comp.GetType().FullName,
                        instanceID = comp.GetInstanceID(),
                        // TODO: Add more component-specific details here if needed in the future?
                        //       Requires reflection or specific handling per component type.
                    })
                    .ToList<object>(); // Explicit cast for clarity if needed

                // 7. Return success response
                return Response.Success(
                    $"Found {componentList.Count} component(s) on asset '{fullPath}'.",
                    componentList
                );
            }
            catch (Exception e)
            {
                Debug.LogError(
                    $"[ManageAsset.GetComponentsFromAsset] Error getting components for '{fullPath}': {e}"
                );
                return Response.Error(
                    $"Error getting components for asset '{fullPath}': {e.Message}"
                );
            }
        }

        // --- Internal Helpers ---

        /// <summary>
        /// Ensures the asset path starts with "Assets/".
        /// </summary>
        private static string SanitizeAssetPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return path;
            path = path.Replace('\\', '/'); // Normalize separators
            if (!path.StartsWith("Assets/", StringComparison.OrdinalIgnoreCase))
            {
                return "Assets/" + path.TrimStart('/');
            }
            return path;
        }

        /// <summary>
        /// Checks if an asset exists at the given path (file or folder).
        /// </summary>
        private static bool AssetExists(string sanitizedPath)
        {
            // AssetDatabase APIs are generally preferred over raw File/Directory checks for assets.
            // Check if it's a known asset GUID.
            if (!string.IsNullOrEmpty(AssetDatabase.AssetPathToGUID(sanitizedPath)))
            {
                return true;
            }
            // AssetPathToGUID might not work for newly created folders not yet refreshed.
            // Check directory explicitly for folders.
            if (Directory.Exists(Path.Combine(Directory.GetCurrentDirectory(), sanitizedPath)))
            {
                // Check if it's considered a *valid* folder by Unity
                return AssetDatabase.IsValidFolder(sanitizedPath);
            }
            // Check file existence for non-folder assets.
            if (File.Exists(Path.Combine(Directory.GetCurrentDirectory(), sanitizedPath)))
            {
                return true; // Assume if file exists, it's an asset or will be imported
            }

            return false;
            // Alternative: return !string.IsNullOrEmpty(AssetDatabase.AssetPathToGUID(sanitizedPath));
        }

        /// <summary>
        /// Ensures the directory for a given asset path exists, creating it if necessary.
        /// </summary>
        private static void EnsureDirectoryExists(string directoryPath)
        {
            if (string.IsNullOrEmpty(directoryPath))
                return;
            string fullDirPath = Path.Combine(Directory.GetCurrentDirectory(), directoryPath);
            if (!Directory.Exists(fullDirPath))
            {
                Directory.CreateDirectory(fullDirPath);
                AssetDatabase.Refresh(); // Let Unity know about the new folder
            }
        }

        /// <summary>
        /// Applies properties from JObject to a Material.
        /// </summary>
        private static bool ApplyMaterialProperties(Material mat, JObject properties)
        {
            if (mat == null || properties == null)
                return false;
            bool modified = false;

            // Example: Set shader
            if (properties["shader"]?.Type == JTokenType.String)
            {
                Shader newShader = Shader.Find(properties["shader"].ToString());
                if (newShader != null && mat.shader != newShader)
                {
                    mat.shader = newShader;
                    modified = true;
                }
            }
            // Example: Set color property
            if (properties["color"] is JObject colorProps)
            {
                string propName = colorProps["name"]?.ToString() ?? "_Color"; // Default main color
                if (colorProps["value"] is JArray colArr && colArr.Count >= 3)
                {
                    try
                    {
                        Color newColor = new Color(
                            colArr[0].ToObject<float>(),
                            colArr[1].ToObject<float>(),
                            colArr[2].ToObject<float>(),
                            colArr.Count > 3 ? colArr[3].ToObject<float>() : 1.0f
                        );
                        if (mat.HasProperty(propName) && mat.GetColor(propName) != newColor)
                        {
                            mat.SetColor(propName, newColor);
                            modified = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning(
                            $"Error parsing color property '{propName}': {ex.Message}"
                        );
                    }
                }
            }
            // Example: Set float property
            if (properties["float"] is JObject floatProps)
            {
                string propName = floatProps["name"]?.ToString();
                if (
                    !string.IsNullOrEmpty(propName) && floatProps["value"]?.Type == JTokenType.Float
                    || floatProps["value"]?.Type == JTokenType.Integer
                )
                {
                    try
                    {
                        float newVal = floatProps["value"].ToObject<float>();
                        if (mat.HasProperty(propName) && mat.GetFloat(propName) != newVal)
                        {
                            mat.SetFloat(propName, newVal);
                            modified = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning(
                            $"Error parsing float property '{propName}': {ex.Message}"
                        );
                    }
                }
            }
            // Example: Set texture property
            if (properties["texture"] is JObject texProps)
            {
                string propName = texProps["name"]?.ToString() ?? "_MainTex"; // Default main texture
                string texPath = texProps["path"]?.ToString();
                if (!string.IsNullOrEmpty(texPath))
                {
                    Texture newTex = AssetDatabase.LoadAssetAtPath<Texture>(
                        SanitizeAssetPath(texPath)
                    );
                    if (
                        newTex != null
                        && mat.HasProperty(propName)
                        && mat.GetTexture(propName) != newTex
                    )
                    {
                        mat.SetTexture(propName, newTex);
                        modified = true;
                    }
                    else if (newTex == null)
                    {
                        Debug.LogWarning($"Texture not found at path: {texPath}");
                    }
                }
            }

            // TODO: Add handlers for other property types (Vectors, Ints, Keywords, RenderQueue, etc.)
            return modified;
        }

        /// <summary>
        /// Generic helper to set properties on any UnityEngine.Object using reflection.
        /// </summary>
        private static bool ApplyObjectProperties(UnityEngine.Object target, JObject properties)
        {
            if (target == null || properties == null)
                return false;
            bool modified = false;
            Type type = target.GetType();

            foreach (var prop in properties.Properties())
            {
                string propName = prop.Name;
                JToken propValue = prop.Value;
                if (SetPropertyOrField(target, propName, propValue, type))
                {
                    modified = true;
                }
            }
            return modified;
        }

        /// <summary>
        /// Helper to set a property or field via reflection, handling basic types and Unity objects.
        /// </summary>
        private static bool SetPropertyOrField(
            object target,
            string memberName,
            JToken value,
            Type type = null
        )
        {
            type = type ?? target.GetType();
            System.Reflection.BindingFlags flags =
                System.Reflection.BindingFlags.Public
                | System.Reflection.BindingFlags.Instance
                | System.Reflection.BindingFlags.IgnoreCase;

            try
            {
                System.Reflection.PropertyInfo propInfo = type.GetProperty(memberName, flags);
                if (propInfo != null && propInfo.CanWrite)
                {
                    object convertedValue = ConvertJTokenToType(value, propInfo.PropertyType);
                    if (
                        convertedValue != null
                        && !object.Equals(propInfo.GetValue(target), convertedValue)
                    )
                    {
                        propInfo.SetValue(target, convertedValue);
                        return true;
                    }
                }
                else
                {
                    System.Reflection.FieldInfo fieldInfo = type.GetField(memberName, flags);
                    if (fieldInfo != null)
                    {
                        object convertedValue = ConvertJTokenToType(value, fieldInfo.FieldType);
                        if (
                            convertedValue != null
                            && !object.Equals(fieldInfo.GetValue(target), convertedValue)
                        )
                        {
                            fieldInfo.SetValue(target, convertedValue);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning(
                    $"[SetPropertyOrField] Failed to set '{memberName}' on {type.Name}: {ex.Message}"
                );
            }
            return false;
        }

        /// <summary>
        /// Simple JToken to Type conversion for common Unity types and primitives.
        /// </summary>
        private static object ConvertJTokenToType(JToken token, Type targetType)
        {
            try
            {
                if (token == null || token.Type == JTokenType.Null)
                    return null;

                if (targetType == typeof(string))
                    return token.ToObject<string>();
                if (targetType == typeof(int))
                    return token.ToObject<int>();
                if (targetType == typeof(float))
                    return token.ToObject<float>();
                if (targetType == typeof(bool))
                    return token.ToObject<bool>();
                if (targetType == typeof(Vector2) && token is JArray arrV2 && arrV2.Count == 2)
                    return new Vector2(arrV2[0].ToObject<float>(), arrV2[1].ToObject<float>());
                if (targetType == typeof(Vector3) && token is JArray arrV3 && arrV3.Count == 3)
                    return new Vector3(
                        arrV3[0].ToObject<float>(),
                        arrV3[1].ToObject<float>(),
                        arrV3[2].ToObject<float>()
                    );
                if (targetType == typeof(Vector4) && token is JArray arrV4 && arrV4.Count == 4)
                    return new Vector4(
                        arrV4[0].ToObject<float>(),
                        arrV4[1].ToObject<float>(),
                        arrV4[2].ToObject<float>(),
                        arrV4[3].ToObject<float>()
                    );
                if (targetType == typeof(Quaternion) && token is JArray arrQ && arrQ.Count == 4)
                    return new Quaternion(
                        arrQ[0].ToObject<float>(),
                        arrQ[1].ToObject<float>(),
                        arrQ[2].ToObject<float>(),
                        arrQ[3].ToObject<float>()
                    );
                if (targetType == typeof(Color) && token is JArray arrC && arrC.Count >= 3) // Allow RGB or RGBA
                    return new Color(
                        arrC[0].ToObject<float>(),
                        arrC[1].ToObject<float>(),
                        arrC[2].ToObject<float>(),
                        arrC.Count > 3 ? arrC[3].ToObject<float>() : 1.0f
                    );
                if (targetType.IsEnum)
                    return Enum.Parse(targetType, token.ToString(), true); // Case-insensitive enum parsing

                // Handle loading Unity Objects (Materials, Textures, etc.) by path
                if (
                    typeof(UnityEngine.Object).IsAssignableFrom(targetType)
                    && token.Type == JTokenType.String
                )
                {
                    string assetPath = SanitizeAssetPath(token.ToString());
                    UnityEngine.Object loadedAsset = AssetDatabase.LoadAssetAtPath(
                        assetPath,
                        targetType
                    );
                    if (loadedAsset == null)
                    {
                        Debug.LogWarning(
                            $"[ConvertJTokenToType] Could not load asset of type {targetType.Name} from path: {assetPath}"
                        );
                    }
                    return loadedAsset;
                }

                // Fallback: Try direct conversion (might work for other simple value types)
                return token.ToObject(targetType);
            }
            catch (Exception ex)
            {
                Debug.LogWarning(
                    $"[ConvertJTokenToType] Could not convert JToken '{token}' (type {token.Type}) to type '{targetType.Name}': {ex.Message}"
                );
                return null;
            }
        }

        /// <summary>
        /// Helper to find a Type by name, searching relevant assemblies.
        /// Needed for creating ScriptableObjects or finding component types by name.
        /// </summary>
        private static Type FindType(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
                return null;

            // Try direct lookup first (common Unity types often don't need assembly qualified name)
            var type =
                Type.GetType(typeName)
                ?? Type.GetType($"UnityEngine.{typeName}, UnityEngine.CoreModule")
                ?? Type.GetType($"UnityEngine.UI.{typeName}, UnityEngine.UI")
                ?? Type.GetType($"UnityEditor.{typeName}, UnityEditor.CoreModule");

            if (type != null)
                return type;

            // If not found, search loaded assemblies (slower but more robust for user scripts)
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                // Look for non-namespaced first
                type = assembly.GetType(typeName, false, true); // throwOnError=false, ignoreCase=true
                if (type != null)
                    return type;

                // Check common namespaces if simple name given
                type = assembly.GetType("UnityEngine." + typeName, false, true);
                if (type != null)
                    return type;
                type = assembly.GetType("UnityEditor." + typeName, false, true);
                if (type != null)
                    return type;
                // Add other likely namespaces if needed (e.g., specific plugins)
            }

            Debug.LogWarning($"[FindType] Type '{typeName}' not found in any loaded assembly.");
            return null; // Not found
        }

        // --- Data Serialization ---

        /// <summary>
        /// Creates a serializable representation of an asset.
        /// </summary>
        private static object GetAssetData(string path, bool generatePreview = false)
        {
            if (string.IsNullOrEmpty(path) || !AssetExists(path))
                return null;

            string guid = AssetDatabase.AssetPathToGUID(path);
            Type assetType = AssetDatabase.GetMainAssetTypeAtPath(path);
            UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(path);
            string previewBase64 = null;
            int previewWidth = 0;
            int previewHeight = 0;

            if (generatePreview && asset != null)
            {
                Texture2D preview = AssetPreview.GetAssetPreview(asset);

                if (preview != null)
                {
                    try
                    {
                        // Ensure texture is readable for EncodeToPNG
                        // Creating a temporary readable copy is safer
                        RenderTexture rt = RenderTexture.GetTemporary(
                            preview.width,
                            preview.height
                        );
                        Graphics.Blit(preview, rt);
                        RenderTexture previous = RenderTexture.active;
                        RenderTexture.active = rt;
                        Texture2D readablePreview = new Texture2D(preview.width, preview.height);
                        readablePreview.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
                        readablePreview.Apply();
                        RenderTexture.active = previous;
                        RenderTexture.ReleaseTemporary(rt);

                        byte[] pngData = readablePreview.EncodeToPNG();
                        previewBase64 = Convert.ToBase64String(pngData);
                        previewWidth = readablePreview.width;
                        previewHeight = readablePreview.height;
                        UnityEngine.Object.DestroyImmediate(readablePreview); // Clean up temp texture
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning(
                            $"Failed to generate readable preview for '{path}': {ex.Message}. Preview might not be readable."
                        );
                        // Fallback: Try getting static preview if available?
                        // Texture2D staticPreview = AssetPreview.GetMiniThumbnail(asset);
                    }
                }
                else
                {
                    Debug.LogWarning(
                        $"Could not get asset preview for {path} (Type: {assetType?.Name}). Is it supported?"
                    );
                }
            }

            return new
            {
                path = path,
                guid = guid,
                assetType = assetType?.FullName ?? "Unknown",
                name = Path.GetFileNameWithoutExtension(path),
                fileName = Path.GetFileName(path),
                isFolder = AssetDatabase.IsValidFolder(path),
                instanceID = asset?.GetInstanceID() ?? 0,
                lastWriteTimeUtc = File.GetLastWriteTimeUtc(
                        Path.Combine(Directory.GetCurrentDirectory(), path)
                    )
                    .ToString("o"), // ISO 8601
                // --- Preview Data ---
                previewBase64 = previewBase64, // PNG data as Base64 string
                previewWidth = previewWidth,
                previewHeight = previewHeight,
                // TODO: Add more metadata? Importer settings? Dependencies?
            };
        }

        // --- Unity 6.2 New Methods ---

        /// <summary>
        /// [UNITY 6.2] - Executa operações em lote para múltiplos assets.
        /// </summary>
        private static object BatchOperations(JObject @params)
        {
            try
            {
                var operations = @params["operations"]?.ToObject<JArray>();
                if (operations == null || operations.Count == 0)
                {
                    return Response.Error("Operations array is required for batch operations");
                }

                var results = new List<object>();
                var successCount = 0;
                var errorCount = 0;

                // Usar batch mode para otimização
                AssetDatabase.StartAssetEditing();

                try
                {
                    foreach (JObject operation in operations)
                    {
                        try
                        {
                            var result = HandleCommand(operation);
                            results.Add(result);

                            if (result.ToString().Contains("Success"))
                                successCount++;
                            else
                                errorCount++;
                        }
                        catch (Exception e)
                        {
                            errorCount++;
                            results.Add(Response.Error($"Batch operation failed: {e.Message}"));
                        }
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                    AssetDatabase.Refresh();
                }

                LogOperation("BatchOperations", $"Completed {operations.Count} operations - Success: {successCount}, Errors: {errorCount}");

                return Response.Success($"Batch operations completed", new
                {
                    totalOperations = operations.Count,
                    successCount = successCount,
                    errorCount = errorCount,
                    results = results.ToArray()
                });
            }
            catch (Exception e)
            {
                LogOperation("BatchOperations", $"Batch operation failed: {e.Message}", true);
                return Response.Error($"Batch operations failed: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Otimiza assets usando recursos avançados do Unity 6.2.
        /// </summary>
        private static object OptimizeAsset(string path, JObject @params)
        {
            try
            {
                string sanitizedPath = SanitizeAssetPath(path);
                if (!AssetExists(sanitizedPath))
                {
                    return Response.Error($"Asset not found: {sanitizedPath}");
                }

                string optimizationType = @params["optimization_type"]?.ToString() ?? "general";
                bool enableGPUOptimization = @params["enable_gpu_optimization"]?.ToObject<bool>() ?? true;

                var optimizations = new List<string>();

                // Otimizações baseadas no tipo de asset
                Type assetType = AssetDatabase.GetMainAssetTypeAtPath(sanitizedPath);

                if (assetType == typeof(Texture2D))
                {
                    optimizations.AddRange(OptimizeTexture(sanitizedPath, @params));
                }
                else if (assetType == typeof(Mesh))
                {
                    optimizations.AddRange(OptimizeMesh(sanitizedPath, @params));
                }
                else if (assetType == typeof(AudioClip))
                {
                    optimizations.AddRange(OptimizeAudio(sanitizedPath, @params));
                }

                // Aplicar otimizações gerais Unity 6.2
                if (enableGPUOptimization && SystemInfo.supportsComputeShaders)
                {
                    optimizations.Add("GPU acceleration enabled");
                }

                AssetDatabase.ImportAsset(sanitizedPath, ImportAssetOptions.ForceUpdate);

                LogOperation("OptimizeAsset", $"Asset optimized: {sanitizedPath}");

                return Response.Success($"Asset optimized successfully", new
                {
                    path = sanitizedPath,
                    assetType = assetType?.Name ?? "Unknown",
                    optimizationType = optimizationType,
                    optimizationsApplied = optimizations.ToArray(),
                    enableGPUOptimization = enableGPUOptimization
                });
            }
            catch (Exception e)
            {
                LogOperation("OptimizeAsset", $"Optimization failed: {e.Message}", true);
                return Response.Error($"Asset optimization failed: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Otimiza texturas usando configurações avançadas.
        /// </summary>
        private static List<string> OptimizeTexture(string path, JObject @params)
        {
            var optimizations = new List<string>();

            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                // Configurações Unity 6.2
                importer.mipmapEnabled = true;
                importer.streamingMipmaps = true;
                importer.crunchedCompression = true;
                importer.compressionQuality = 50;

                optimizations.Add("Mipmap streaming enabled");
                optimizations.Add("Crunch compression enabled");

                // Configurar para diferentes plataformas
                var platformSettings = importer.GetDefaultPlatformTextureSettings();
                platformSettings.compressionQuality = 50;
                platformSettings.crunchedCompression = true;
                importer.SetPlatformTextureSettings(platformSettings);

                optimizations.Add("Platform-specific compression configured");
            }

            return optimizations;
        }

        /// <summary>
        /// [UNITY 6.2] - Otimiza meshes usando Mesh API avançada.
        /// </summary>
        private static List<string> OptimizeMesh(string path, JObject @params)
        {
            var optimizations = new List<string>();

            ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;
            if (importer != null)
            {
                // Configurações Unity 6.2
                importer.optimizeMeshPolygons = true;
                importer.optimizeMeshVertices = true;
                importer.weldVertices = true;
                importer.indexFormat = ModelImporterIndexFormat.Auto;

                optimizations.Add("Mesh polygon optimization enabled");
                optimizations.Add("Vertex optimization enabled");
                optimizations.Add("Vertex welding enabled");
                optimizations.Add("Auto index format selection");
            }

            return optimizations;
        }

        /// <summary>
        /// [UNITY 6.2] - Otimiza audio clips.
        /// </summary>
        private static List<string> OptimizeAudio(string path, JObject @params)
        {
            var optimizations = new List<string>();

            AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
            if (importer != null)
            {
                // Configurações Unity 6.2
                var settings = importer.defaultSampleSettings;
                settings.compressionFormat = AudioCompressionFormat.Vorbis;
                settings.quality = 0.7f;
                settings.loadType = AudioClipLoadType.CompressedInMemory;

                importer.defaultSampleSettings = settings;

                optimizations.Add("Vorbis compression enabled");
                optimizations.Add("Compressed in-memory loading");
                optimizations.Add("Quality optimized for size");
            }

            return optimizations;
        }

        /// <summary>
        /// [UNITY 6.2] - Analisa dependências de assets.
        /// </summary>
        private static object AnalyzeDependencies(string path)
        {
            try
            {
                string sanitizedPath = SanitizeAssetPath(path);
                if (!AssetExists(sanitizedPath))
                {
                    return Response.Error($"Asset not found: {sanitizedPath}");
                }

                string[] dependencies = AssetDatabase.GetDependencies(sanitizedPath, true);
                string[] directDependencies = AssetDatabase.GetDependencies(sanitizedPath, false);

                var dependencyInfo = dependencies.Select(dep => new
                {
                    path = dep,
                    type = AssetDatabase.GetMainAssetTypeAtPath(dep)?.Name ?? "Unknown",
                    size = GetAssetSize(dep),
                    isDirect = directDependencies.Contains(dep)
                }).ToArray();

                LogOperation("AnalyzeDependencies", $"Analyzed {dependencies.Length} dependencies for {sanitizedPath}");

                return Response.Success("Dependencies analyzed successfully", new
                {
                    assetPath = sanitizedPath,
                    totalDependencies = dependencies.Length,
                    directDependencies = directDependencies.Length,
                    dependencies = dependencyInfo
                });
            }
            catch (Exception e)
            {
                LogOperation("AnalyzeDependencies", $"Analysis failed: {e.Message}", true);
                return Response.Error($"Dependency analysis failed: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Valida assets do projeto.
        /// </summary>
        private static object ValidateAssets(JObject @params)
        {
            try
            {
                string searchPath = @params["search_path"]?.ToString() ?? "Assets";
                bool checkMissingReferences = @params["check_missing_references"]?.ToObject<bool>() ?? true;
                bool checkDuplicates = @params["check_duplicates"]?.ToObject<bool>() ?? true;

                var issues = new List<object>();
                var validAssets = 0;

                string[] allAssets = AssetDatabase.FindAssets("", new[] { searchPath });

                foreach (string guid in allAssets)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);

                    if (checkMissingReferences)
                    {
                        var missingRefs = FindMissingReferences(assetPath);
                        if (missingRefs.Any())
                        {
                            issues.Add(new
                            {
                                type = "missing_reference",
                                path = assetPath,
                                details = missingRefs
                            });
                        }
                    }

                    validAssets++;
                }

                LogOperation("ValidateAssets", $"Validated {validAssets} assets, found {issues.Count} issues");

                return Response.Success("Asset validation completed", new
                {
                    searchPath = searchPath,
                    totalAssets = validAssets,
                    issuesFound = issues.Count,
                    issues = issues.ToArray()
                });
            }
            catch (Exception e)
            {
                LogOperation("ValidateAssets", $"Validation failed: {e.Message}", true);
                return Response.Error($"Asset validation failed: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Comprime texturas em lote.
        /// </summary>
        private static object CompressTextures(JObject @params)
        {
            try
            {
                string searchPath = @params["search_path"]?.ToString() ?? "Assets";
                string compressionFormat = @params["compression_format"]?.ToString() ?? "DXT5";
                int quality = @params["quality"]?.ToObject<int>() ?? 50;

                string[] textureGuids = AssetDatabase.FindAssets("t:Texture2D", new[] { searchPath });
                var results = new List<object>();

                AssetDatabase.StartAssetEditing();

                try
                {
                    foreach (string guid in textureGuids)
                    {
                        string texturePath = AssetDatabase.GUIDToAssetPath(guid);
                        var result = CompressSingleTexture(texturePath, compressionFormat, quality);
                        results.Add(result);
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                    AssetDatabase.Refresh();
                }

                LogOperation("CompressTextures", $"Compressed {textureGuids.Length} textures");

                return Response.Success("Texture compression completed", new
                {
                    searchPath = searchPath,
                    texturesProcessed = textureGuids.Length,
                    compressionFormat = compressionFormat,
                    quality = quality,
                    results = results.ToArray()
                });
            }
            catch (Exception e)
            {
                LogOperation("CompressTextures", $"Compression failed: {e.Message}", true);
                return Response.Error($"Texture compression failed: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera previews para assets.
        /// </summary>
        private static object GeneratePreviews(JObject @params)
        {
            try
            {
                string searchPath = @params["search_path"]?.ToString() ?? "Assets";
                int previewSize = @params["preview_size"]?.ToObject<int>() ?? 256;

                string[] assetGuids = AssetDatabase.FindAssets("", new[] { searchPath });
                var results = new List<object>();

                foreach (string guid in assetGuids)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var assetData = GetAssetData(assetPath, true);
                    if (assetData != null)
                    {
                        results.Add(assetData);
                    }
                }

                LogOperation("GeneratePreviews", $"Generated previews for {results.Count} assets");

                return Response.Success("Preview generation completed", new
                {
                    searchPath = searchPath,
                    previewSize = previewSize,
                    previewsGenerated = results.Count,
                    previews = results.ToArray()
                });
            }
            catch (Exception e)
            {
                LogOperation("GeneratePreviews", $"Preview generation failed: {e.Message}", true);
                return Response.Error($"Preview generation failed: {e.Message}");
            }
        }

        /// <summary>
        /// [HELPER] - Obtém o tamanho de um asset em bytes.
        /// </summary>
        private static long GetAssetSize(string path)
        {
            try
            {
                string fullPath = Path.Combine(Directory.GetCurrentDirectory(), path);
                if (File.Exists(fullPath))
                {
                    return new FileInfo(fullPath).Length;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// [HELPER] - Encontra referências perdidas em um asset.
        /// </summary>
        private static List<string> FindMissingReferences(string path)
        {
            var missingRefs = new List<string>();

            try
            {
                UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(path);
                if (asset != null)
                {
                    // Implementação básica - pode ser expandida
                    SerializedObject so = new SerializedObject(asset);
                    SerializedProperty prop = so.GetIterator();

                    while (prop.NextVisible(true))
                    {
                        if (prop.propertyType == SerializedPropertyType.ObjectReference)
                        {
                            if (prop.objectReferenceValue == null && prop.hasVisibleChildren)
                            {
                                missingRefs.Add(prop.propertyPath);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Could not check missing references for {path}: {e.Message}");
            }

            return missingRefs;
        }

        /// <summary>
        /// [HELPER] - Comprime uma única textura.
        /// </summary>
        private static object CompressSingleTexture(string path, string format, int quality)
        {
            try
            {
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
                if (importer != null)
                {
                    var settings = importer.GetDefaultPlatformTextureSettings();
                    settings.compressionQuality = quality;
                    settings.crunchedCompression = true;

                    importer.SetPlatformTextureSettings(settings);
                    AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);

                    return new { path = path, success = true, format = format, quality = quality };
                }

                return new { path = path, success = false, error = "Not a texture or importer not found" };
            }
            catch (Exception e)
            {
                return new { path = path, success = false, error = e.Message };
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Aplica propriedades a um AssetImporter.
        /// </summary>
        private static void ApplyImporterProperties(AssetImporter importer, JObject properties)
        {
            if (importer == null || properties == null) return;

            try
            {
                foreach (var property in properties)
                {
                    string propertyName = property.Key;
                    var value = property.Value;

                    // Handle common importer properties
                    switch (propertyName)
                    {
                        case "userData":
                            importer.userData = value?.ToString();
                            break;
                        case "assetBundleName":
                            importer.assetBundleName = value?.ToString();
                            break;
                        case "assetBundleVariant":
                            importer.assetBundleVariant = value?.ToString();
                            break;
                        default:
                            // Try to set the property directly using reflection
                            SetPropertyOrField(importer, propertyName, value);
                            break;
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to apply some importer properties: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Sistema de logging para operações.
        /// </summary>
        private static void LogOperation(string operation, string details, bool isError = false)
        {
            string message = $"[ManageAsset] {operation}: {details}";
            if (isError)
                Debug.LogError(message);
            else
                Debug.Log(message);
        }
    }
}

