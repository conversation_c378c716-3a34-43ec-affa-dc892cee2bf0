#!/usr/bin/env python3
"""
Script para mapear todos os comandos MCP disponíveis no Unity MCP Server
"""
import os
import ast
import glob
import json
from typing import Dict, List, Any

def extract_mcp_tools_from_file(file_path: str) -> List[Dict[str, Any]]:
    """Extrai informações sobre ferramentas MCP de um arquivo Python."""
    tools = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        # Procurar por funções decoradas com @mcp.tool() dentro de funções de registro
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Procurar por funções aninhadas com decorator @mcp.tool()
                for child in ast.walk(node):
                    if isinstance(child, ast.FunctionDef) and child != node:
                        # Verificar se tem decorator @mcp.tool()
                        has_mcp_decorator = False
                        for decorator in child.decorator_list:
                            if (isinstance(decorator, ast.Call) and
                                isinstance(decorator.func, ast.Attribute) and
                                decorator.func.attr == 'tool'):
                                has_mcp_decorator = True
                                break
                            elif (isinstance(decorator, ast.Attribute) and
                                  decorator.attr == 'tool'):
                                has_mcp_decorator = True
                                break

                        if has_mcp_decorator:
                            # Extrair informações da função
                            tool_info = {
                                'name': child.name,
                                'file': os.path.basename(file_path),
                                'parameters': [],
                                'docstring': ast.get_docstring(child) or "No documentation"
                            }

                            # Extrair parâmetros
                            for arg in child.args.args:
                                if arg.arg not in ['ctx', 'self']:  # Ignorar ctx e self
                                    param_info = {'name': arg.arg, 'type': 'Any'}
                                    if arg.annotation:
                                        if isinstance(arg.annotation, ast.Name):
                                            param_info['type'] = arg.annotation.id
                                        elif isinstance(arg.annotation, ast.Constant):
                                            param_info['type'] = str(arg.annotation.value)
                                    tool_info['parameters'].append(param_info)

                            tools.append(tool_info)
                    
    except Exception as e:
        print(f"Erro ao processar {file_path}: {e}")
    
    return tools

def scan_all_mcp_tools() -> Dict[str, Any]:
    """Escaneia todos os arquivos Python em busca de ferramentas MCP."""
    tools_dir = 'UnityMcpServer/src/tools'
    all_tools = []
    categories = {}
    
    print("🔍 Escaneando ferramentas MCP...")
    
    for py_file in glob.glob(os.path.join(tools_dir, '*.py')):
        if '__init__.py' in py_file or '__pycache__' in py_file:
            continue
        
        file_tools = extract_mcp_tools_from_file(py_file)
        if file_tools:
            filename = os.path.basename(py_file)
            print(f"  📁 {filename}: {len(file_tools)} ferramentas")
            
            # Categorizar por nome do arquivo
            category = filename.replace('.py', '').replace('_', ' ').title()
            categories[category] = file_tools
            all_tools.extend(file_tools)
    
    return {
        'total_tools': len(all_tools),
        'total_files': len(categories),
        'tools': all_tools,
        'categories': categories
    }

def generate_test_plan(inventory: Dict[str, Any]) -> str:
    """Gera um plano de testes baseado no inventário."""
    plan = f"""
# 🧪 PLANO DE TESTES MCP UNITY SERVER

## 📊 Resumo do Inventário
- **Total de Ferramentas**: {inventory['total_tools']}
- **Total de Arquivos**: {inventory['total_files']}

## 📋 Categorias de Ferramentas

"""
    
    for category, tools in inventory['categories'].items():
        plan += f"### {category} ({len(tools)} ferramentas)\n"
        for tool in tools:
            plan += f"- `{tool['name']}` - {len(tool['parameters'])} parâmetros\n"
        plan += "\n"
    
    return plan

def main():
    """Função principal."""
    print("🚀 Iniciando inventário de comandos MCP...")
    
    # Verificar se estamos no diretório correto
    if not os.path.exists('UnityMcpServer/src/tools'):
        print("❌ Diretório UnityMcpServer/src/tools não encontrado!")
        print("   Execute este script a partir do diretório raiz do projeto.")
        return
    
    # Escanear todas as ferramentas
    inventory = scan_all_mcp_tools()
    
    # Salvar inventário em JSON
    with open('mcp_tools_inventory.json', 'w', encoding='utf-8') as f:
        json.dump(inventory, f, indent=2, ensure_ascii=False)
    
    # Gerar plano de testes
    test_plan = generate_test_plan(inventory)
    with open('mcp_test_plan.md', 'w', encoding='utf-8') as f:
        f.write(test_plan)
    
    print(f"\n✅ Inventário completo!")
    print(f"   📊 {inventory['total_tools']} ferramentas em {inventory['total_files']} arquivos")
    print(f"   📄 Inventário salvo em: mcp_tools_inventory.json")
    print(f"   📋 Plano de testes salvo em: mcp_test_plan.md")

if __name__ == "__main__":
    main()
