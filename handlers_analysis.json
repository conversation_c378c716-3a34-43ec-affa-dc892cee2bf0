{"python_commands_count": 417, "csharp_handlers_count": 119, "missing_in_csharp": ["HandleAddGeneratedAudioToTimeline", "HandleAddRaytracingInstances", "HandleAdvancedAiQuickSetup", "HandleAdvancedAiSystem", "HandleAdvancedAiSystemInfo", "HandleAiAdaptiveJungle", "HandleAnalyzePerformanceProfile", "HandleAssetManagementWithAssistant", "HandleAssistantWorkflowAutomation", "HandleAttachBehaviourScriptToState", "HandleAutomateTasksWithAssistant", "HandleBatchCharacterCreator", "HandleConfigureAdaptiveQuality", "HandleConfigureAiBillingOptimization", "HandleConfigureAiCloudServices", "HandleConfigureAiGeneratorStyles", "HandleConfigureAiQuantization", "HandleConfigureAntiCheat", "HandleConfigureAreaMasks", "HandleConfigureAudioDspBufferSize", "HandleConfigureBuildPipeline", "HandleConfigureBuildProfileOverrides", "HandleConfigureCloudTesting", "HandleConfigureCompressionMethodRuntime", "HandleConfigureDeferredPlusPerformance", "HandleConfigureDistributedAuthority", "HandleConfigureForwardPlusTransparency", "HandleConfigureGameStateManager", "HandleConfigureGpuInstancingExtensions", "HandleConfigureGraphicsApiRuntime", "HandleConfigureGraphicsStateOptimization", "HandleConfigureInputHaptics", "HandleConfigureInputProcessors", "HandleConfigureLagCompensation", "HandleConfigureLightmapQualitySettings", "HandleConfigureMainMenuCustomization", "HandleConfigureMask64fieldControls", "HandleConfigureMemoryProfiling", "HandleConfigureMultiplayerNetcode", "HandleConfigureNavmeshAreaMasks", "HandleConfigureNavmeshCosts", "HandleConfigurePhysicsMaterialsRuntime", "HandleConfigurePhysicsPerformanceProfiling", "HandleConfigurePsoPrewarming", "HandleConfigureQualitySettingsRuntime", "HandleConfigureRaytracingPipeline", "HandleConfigureReadWriteTextures", "HandleConfigureRealtimeAudioProfiling", "HandleConfigureRuntimeDiagnostics", "HandleConfigureSensorDataClassification", "HandleConfigureSessionMigration", "HandleConfigureShaderVariantStripping", "HandleConfigureTileSetRules", "HandleConfigureTileSets", "HandleConfigureUiRendererOptimizations", "HandleConfigureWaterMaskSystem", "HandleConfigureWaterRollingWaves", "HandleConfigureWebgpuIndirectRendering", "HandleConnectToMultiplayerServer", "HandleControlPostProcessingOnTimeline", "HandleConvertAudioFormat", "HandleCreate2dPhysicsSystem", "HandleCreateAdaptiveAudioSystem", "HandleCreateAiBehaviorBranches", "HandleCreateAiPerception", "HandleCreateAmbientOcclusion", "HandleCreateArFoundationSetup", "HandleCreateArOcclusion", "HandleCreateAssetUsageReport", "HandleCreateAudioEffectsChain", "HandleCreateAudioMixer", "HandleCreateAudioSnapshots", "HandleCreateAudioTriggers", "HandleCreateAudioVisualization", "HandleCreateAutoTilingSystem", "HandleCreateBiomeSystem", "HandleCreateBuildStepTimingReport", "HandleCreateCaustics", "HandleCreateCinemachineSetup", "HandleCreateClothSimulation", "HandleCreateColorGrading", "HandleCreateDayNightCycle", "HandleCreateDeferredPlusMaterialSystem", "HandleCreateDeferredPlusRendering", "HandleCreateDestructionSystem", "HandleCreateDynamicBuildVariants", "HandleCreateDynamicMusic", "HandleCreateDynamicPathModifiers", "HandleCreateDynamicShotList", "HandleCreateDynamicSkillTree", "HandleCreateDynamicUiElements", "HandleCreateExplosionEffects", "HandleCreateFacialAnimation", "HandleCreateFactionSimulationSystem", "HandleCreateFireEffects", "HandleCreateGenerativeStateMachine", "HandleCreateGenerativeTileSet", "HandleCreateGravityZones", "HandleCreateHologramEffects", "HandleCreateInputActionMaps", "HandleCreateInputRebindingUi", "HandleCreateLandmarkGeneration", "HandleCreateLightCookies", "HandleCreateLightProbes", "HandleCreateLightmapVisualizationTools", "HandleCreateLightmaps", "HandleCreateMagneticFields", "HandleCreateMeshColliders", "HandleCreateMixedRealitySetup", "HandleCreateMotionBlur", "HandleCreateNarrativeEventChain", "HandleCreateNaturalLanguageProcessing", "HandleCreateObjectiveTrackerSystem", "HandleCreateParticlePhysics", "HandleCreateParticleSystems", "HandleCreatePhysicsConstraints", "HandleCreatePhysicsEventsSystem", "HandleCreatePlaceholderBehaviorNodes", "HandleCreatePostProcessing", "HandleCreateProceduralAudio", "HandleCreateProceduralBuildingGenerator", "HandleCreateProceduralRoadNetwork", "HandleCreateQuestObjectiveGenerator", "HandleCreateQuestRewardGenerator", "HandleCreateRagdollPhysics", "HandleCreateReverbZones", "HandleCreateRigidbodySystem", "HandleCreateRopePhysics", "HandleCreateSessionPersistence", "HandleCreateShaderVariantCollection", "HandleCreateSimulatedEcosystem", "HandleCreateSpatialAnchors", "HandleCreateTeleportationSystem", "HandleCreateTerrain", "HandleCreateTerrainDetails", "HandleCreateTerrainTrees", "HandleCreateTextureAtlas", "HandleCreateTextureVariationsAi", "HandleCreateTrailEffects", "HandleCreateTriggerSystems", "HandleCreateUiDataBinding", "HandleCreateUiEffects", "HandleCreateUvMappings", "HandleCreateVolumetricLighting", "HandleCreateVrPlayerRig", "HandleCreateWaterCausticsSystem", "HandleCreateWaterFoamSystem", "HandleCreateWaterLineTransition", "HandleCreateWaterSystem", "HandleCreateWeatherEffects", "HandleCreateWebgpuVfxSystem", "HandleCreateXrAnalytics", "HandleCreateXrInputSystem", "HandleCreateXrUiSystem", "HandleCullRaytracingInstances", "HandleGenerate2dLevelLayout", "HandleGenerateActionNodesFromDescription", "HandleGenerateAiAnimation", "HandleGenerateAiBehaviourTree", "HandleGenerateAiMaterial", "HandleGenerateAiSound", "HandleGenerateAiSprite", "HandleGenerateAiTexture", "HandleGenerateBehaviorNodesWithAi", "HandleGenerateBranchingDialogueTree", "HandleGenerateCodeWithAssistant", "HandleGenerateDynamicPointsOfInterest", "HandleGenerateGameplayRulesEngine", "HandleGenerateLSystemStructures", "HandleGenerateLodMeshes", "HandleGenerateMaterialFromTextureAi", "HandleGenerateNpcBackstory", "HandleGenerateParticleSystem", "HandleGenerateQuestStateMachine", "HandleGenerateTilemapFromSet", "HandleGenerateTimelineFromEvents", "HandleGetConnectedPlayers", "HandleGetMultiplayerStatus", "HandleJoinMultiplayerLobby", "HandleLinkQuestsToGameplayEvents", "HandleManageAiPointsSystem", "HandleManageBehaviorGraph", "HandleManageGameobject", "HandleMonitorAiUsageAnalytics", "HandleMonitorNetworkPerformance", "HandleMonitorRuntimePerformance", "HandleOptimizeAssetPipeline", "HandleOptimizeAssets", "HandleOptimizeAudioPerformance", "HandleOptimizeBandwidthUsage", "HandleOptimizeBehaviorTreeWithAi", "HandleOptimizeBuildSize", "HandleOptimizeExistingCode", "HandleOptimizeGenerativeTilemap", "HandleOptimizeInference", "HandleOptimizeMemoryUsage", "HandleOptimizePhysicsPerformance", "HandleOptimizeProceduralGeometry", "HandleOptimizeRenderingPerformance", "HandleOptimizeRenderingPipeline", "HandleOptimizeTerrainPerformance", "HandleOptimizeVfxPerformance", "HandlePaintTerrainTextures", "HandleProjectAnalysisWithAssistant", "HandleRefactorCodeStructure", "HandleRefineAiGeneratedAssets", "HandleRemoveRaytracingInstance", "HandleRunUnityTestSuite", "HandleSceneEditingWithAssistant", "HandleSendMultiplayerMessage", "HandleSequenceGeneratedAnimationsOnTimeline", "HandleSetup2dAnimationRuntime", "HandleSetup3dWaterDeformation", "HandleSetupAdaptiveDifficulty", "HandleSetupAiAssetCloudSync", "HandleSetupAiAssetPipeline", "HandleSetupAiAssistant", "HandleSetupAiCommunication", "HandleSetupAiOptimization", "HandleSetupAssetDependencyAnalysis", "HandleSetupAudioCompression", "HandleSetupAudioDspTimeSync", "HandleSetupAudioFilters", "HandleSetupAudioFormatConversion", "HandleSetupAudioMixerSnapshotBlending", "HandleSetupAudioOcclusion", "HandleSetupAudioStreaming", "HandleSetupAudioVirtualizationRuntime", "HandleSetupAutomatedBuildPipeline", "HandleSetupBicubicLightmapSampling", "HandleSetupBinauralAudio", "HandleSetupBloomEffects", "HandleSetupBuffDebuffSystem", "HandleSetupBuoyancySystem", "HandleSetupCinemachineProceduralCamera", "HandleSetupCollisionLayers", "HandleSetupComfortSettings", "HandleSetupComputeSkinning", "HandleSetupCrossPlatformXr", "HandleSetupCrowdSimulation", "HandleSetupCustomBackendDispatching", "HandleSetupDedicatedServers", "HandleSetupDeferredPlusDebugViews", "HandleSetupDeferredPlusLighting", "HandleSetupDeferredPlusPipeline", "HandleSetupDeferredPlusShaderVariants", "HandleSetupDepthOfField", "HandleSetupDistortionEffects", "HandleSetupDomainReloadAnalysis", "HandleSetupDynamicEventSpawner", "HandleSetupDynamicLoreSystem", "HandleSetupDynamicNavmeshAreas", "HandleSetupEnvironmentZones", "HandleSetupEnvironmentalEffects", "HandleSetupEyeTracking", "HandleSetupFluidSimulation", "HandleSetupFogEffects", "HandleSetupFrameSlicing", "HandleSetupGenerativeAiBehaviorGraph", "HandleSetupGeometryScriptingRuntime", "HandleSetupGlobalIllumination", "HandleSetupGraphicsOverrideRuntime", "HandleSetupGraphicsStateCollection", "HandleSetupGraphicsStateSerialization", "HandleSetupGraphicsStateTracing", "HandleSetupHandTracking", "HandleSetupHapticFeedback", "HandleSetupHdrPipeline", "HandleSetupIkSystems", "HandleSetupInferenceDialogueGenerator", "HandleSetupInferenceRuntime", "HandleSetupInputDeviceManagement", "HandleSetupInputEventRouting", "HandleSetupInputSystemRuntime", "HandleSetupJointSystems", "HandleSetupLightShafts", "HandleSetupLightmapEdgeSmoothing", "HandleSetupLightmapPerformanceAnalysis", "HandleSetupMachineLearning", "HandleSetupMagicEffects", "HandleSetupMotionCapture", "HandleSetupMotionMatching", "HandleSetupMultiplayerSessions", "HandleSetupNavmeshLinks", "HandleSetupNavmeshSystem", "HandleSetupNetworkCulling", "HandleSetupObjectRecognition", "HandleSetupPassthroughAr", "HandleSetupPathfinding", "HandleSetupPerformanceBudgets", "HandleSetupPhysicsDebugVisualization", "HandleSetupPhysicsLayersRuntime", "HandleSetupPhysicsMaterials", "HandleSetupPhysicsOptimization", "HandleSetupPipelineStateTracing", "HandleSetupPlatformSpecificSettings", "HandleSetupProceduralCaves", "HandleSetupProceduralEventSystem", "HandleSetupProjectAuditor", "HandleSetupProjectDependencies", "HandleSetupRealtimeGi", "HandleSetupReflectionProbes", "HandleSetupRoomScaleTracking", "HandleSetupRuntimeAssetOverrides", "HandleSetupRuntimeObstacleAvoidance", "HandleSetupScreenEffects", "HandleSetupScreenSpaceReflections", "HandleSetupScriptSaveLocationHandler", "HandleSetupSessionMatchmaking", "HandleSetupShaderCompilationMonitoring", "HandleSetupShaderVariantAnalysis", "HandleSetupShaderVariantCollection", "HandleSetupShadowCascades", "HandleSetupSoftBodyPhysics", "HandleSetupSpatialAudio", "HandleSetupSpriteAtlasRuntime", "HandleSetupTerrainCollision", "HandleSetupTerrainGrass", "HandleSetupTileSetVariations", "HandleSetupTilemapGeneration", "HandleSetupTimelineSequences", "HandleSetupUiProfilerMarkers", "HandleSetupUiRuntimeEvents", "HandleSetupUiToolkitAnimations", "HandleSetupUiToolkitRuntime", "HandleSetupUnityAi", "HandleSetupUnityCloudAiProject", "HandleSetupUnityGamingServices", "HandleSetupVariableRateShading", "HandleSetupVehiclePhysics", "HandleSetupVersionControlIntegration", "HandleSetupVfxGraph", "HandleSetupVoiceChat", "HandleSetupWaterCausticsBuffer", "HandleSetupWaterEffects", "HandleSetupWaterFlowMaps", "HandleSetupWaterQueryApi", "HandleSetupWeatherSystem", "HandleSetupWebgpuCompatibilityDetection", "HandleSetupWebgpuComputeShaders", "HandleSetupWebgpuGpuSkinning", "HandleSetupWindSystem", "HandleSetupWorldStateChangeSystem", "HandleSetupXrPerformanceOptimization", "HandleSetupXrToolkit", "HandleSimulateNpcDailyRoutines", "HandleStartMultiplayerHost", "HandleSyncPlayerData", "HandleTriggerCinematicFromGameplay", "HandleTroubleshootWithAssistant", "HandleUpdateAccelerationStructure", "HandleUpdateRaytracingGeometry", "HandleValidateGeneratedBehaviorCode", "HandleValidateProjectIntegrity"], "missing_in_python": ["HandleAIAdaptiveJungle", "HandleAIAssetGeneration", "HandleAIBehaviourTree", "HandleAIRuntime", "HandleAdvancedAI", "HandleAdvancedAudio", "HandleAdvancedPhysics", "HandleAdvancedRendering", "HandleAnimationRuntime", "HandleAssetProcessing", "HandleAttachBehaviourScript", "HandleAudioSystem", "HandleAutomatedCinematics", "HandleBehaviorAIGeneration", "HandleBuffDebuffSystem", "HandleBuildProfilesRuntime", "HandleDeferredPlusRendering", "HandleDistributedAuthority", "HandleDynamicGameSystems", "HandleDynamicSkillTree", "HandleEditorBuildTools", "HandleExecuteMenuItem", "HandleGameStateManager", "HandleGameplayRulesEngine", "HandleGenerative2DWorld", "HandleGenerativeStateMachine", "HandleGraphicsStateCollection", "HandleInferenceNeuralNetwork", "HandleLightingRendering", "HandleManageAsset", "HandleManageGameObject", "HandleMultiplayerNetworking", "HandleMultiplayerSessions", "HandleNavigationEnhanced", "HandleObjectiveTrackerSystem", "HandleProceduralCharacterAssembly", "HandleProceduralEventSystem", "HandleProceduralNarrative", "HandleProceduralObjectives", "HandleProjectAuditorRuntime", "HandleProjectCodeOperations", "HandleRaytracingOperations", "HandleTerrainEnvironment", "HandleTwoDToolkitRuntime", "HandleUIToolkitRuntime", "HandleUnityAIAssistant", "HandleUnityCloudAI", "HandleUnityTesting", "HandleWaterSystemRuntime", "HandleWebGPUCompatibilityDetection", "HandleWebGPUGPUSkinning", "HandleWebGPUIndirectRendering", "HandleWebGPURuntime", "HandleWebGPUVFXSystem", "HandleWorldSimulationSystems", "HandleXRRuntime"], "python_commands": ["HandleAddGeneratedAudioToTimeline", "HandleAddRaytracingInstances", "HandleAdvancedAiQuickSetup", "HandleAdvancedAiSystem", "HandleAdvancedAiSystemInfo", "HandleAiAdaptiveJungle", "HandleAnalyzeGameplayDataForBalancing", "HandleAnalyzePerformanceProfile", "HandleAssetManagementWithAssistant", "HandleAssistantWorkflowAutomation", "HandleAttachBehaviourScriptToState", "HandleAutomateTasksWithAssistant", "HandleBalanceSkillCostsAndEffects", "HandleBatchCharacterCreator", "HandleChampionFusionSystem", "HandleCharacterGameplaySystem", "HandleConfigureAdaptiveQuality", "HandleConfigureAiBillingOptimization", "HandleConfigureAiCloudServices", "HandleConfigureAiGeneratorStyles", "HandleConfigureAiQuantization", "HandleConfigureAntiCheat", "HandleConfigureAreaMasks", "HandleConfigureAssetUsageAnalysis", "HandleConfigureAudioDspBufferSize", "HandleConfigureBuildPipeline", "HandleConfigureBuildProfileOverrides", "HandleConfigureBuildSizeAnalysis", "HandleConfigureCloudTesting", "HandleConfigureCompressionMethodRuntime", "HandleConfigureDeferredPlusPerformance", "HandleConfigureDistributedAuthority", "HandleConfigureForwardPlusTransparency", "HandleConfigureGameStateManager", "HandleConfigureGpuInstancingExtensions", "HandleConfigureGraphicsApiRuntime", "HandleConfigureGraphicsStateOptimization", "HandleConfigureInferenceOnnxImport", "HandleConfigureInferenceTensorOperations", "HandleConfigureInputHaptics", "HandleConfigureInputProcessors", "HandleConfigureLagCompensation", "HandleConfigureLightmapQualitySettings", "HandleConfigureMainMenuCustomization", "HandleConfigureMask64fieldControls", "HandleConfigureMemoryProfiling", "HandleConfigureMultiplayerNetcode", "HandleConfigureNavmeshAreaMasks", "HandleConfigureNavmeshCosts", "HandleConfigurePhysicsMaterialsRuntime", "HandleConfigurePhysicsPerformanceProfiling", "HandleConfigurePsoPrewarming", "HandleConfigureQualitySettingsRuntime", "HandleConfigureRaytracingPipeline", "HandleConfigureReadWriteTextures", "HandleConfigureRealtimeAudioProfiling", "HandleConfigureRuntimeDiagnostics", "HandleConfigureSensorDataClassification", "HandleConfigureSessionMigration", "HandleConfigureShaderGraphVfxSupport", "HandleConfigureShaderVariantStripping", "HandleConfigureShadingRateImage", "HandleConfigureTileSetRules", "HandleConfigureTileSets", "HandleConfigureUiRendererOptimizations", "HandleConfigureVfxGpuInstancing", "HandleConfigureVrsDebugVisualization", "HandleConfigureWaterMaskSystem", "HandleConfigureWaterRollingWaves", "HandleConfigureWebgpuIndirectRendering", "HandleConnectToMultiplayerServer", "HandleControlPostProcessingOnTimeline", "HandleConvertAudioFormat", "HandleCreate2dPhysicsSystem", "HandleCreateAdaptiveAudioSystem", "HandleCreateAiBehaviorBranches", "HandleCreateAiPerception", "HandleCreateAmbientOcclusion", "HandleCreateArFoundationSetup", "HandleCreateArOcclusion", "HandleCreateAssetUsageReport", "HandleCreateAudioEffectsChain", "HandleCreateAudioMixer", "HandleCreateAudioSnapshots", "HandleCreateAudioTriggers", "HandleCreateAudioVisualization", "HandleCreateAutoTilingSystem", "HandleCreateBiomeSystem", "HandleCreateBuildStepTimingReport", "HandleCreateCaustics", "HandleCreateCinemachineSetup", "HandleCreateClothSimulation", "HandleCreateColorGrading", "HandleCreateDayNightCycle", "HandleCreateDeferredPlusMaterialSystem", "HandleCreateDeferredPlusRendering", "HandleCreateDestructionSystem", "HandleCreateDynamicBuildVariants", "HandleCreateDynamicMusic", "HandleCreateDynamicPathModifiers", "HandleCreateDynamicShopInventory", "HandleCreateDynamicShotList", "HandleCreateDynamicSkillTree", "HandleCreateDynamicUiElements", "HandleCreateEconomyEventSimulator", "HandleCreateExplosionEffects", "HandleCreateFacialAnimation", "HandleCreateFactionSimulationSystem", "HandleCreateFireEffects", "HandleCreateGenerativeStateMachine", "HandleCreateGenerativeTileSet", "HandleCreateGravityZones", "HandleCreateHologramEffects", "HandleCreateInferenceModelVisualization", "HandleCreateInferenceQuantizationSystem", "HandleCreateInputActionMaps", "HandleCreateInputRebindingUi", "HandleCreateLandmarkGeneration", "HandleCreateLightCookies", "HandleCreateLightProbes", "HandleCreateLightmapVisualizationTools", "HandleCreateLightmaps", "HandleCreateMagneticFields", "HandleCreateMemoryProfilingReport", "HandleCreateMeshColliders", "HandleCreateMixedRealitySetup", "HandleCreateMotionBlur", "HandleCreateNarrativeEventChain", "HandleCreateNaturalLanguageProcessing", "HandleCreateObjectiveTrackerSystem", "HandleCreateParticlePhysics", "HandleCreateParticleSystems", "HandleCreatePerformanceRecommendations", "HandleCreatePhysicsConstraints", "HandleCreatePhysicsEventsSystem", "HandleCreatePlaceholderBehaviorNodes", "HandleCreatePostProcessing", "HandleCreateProceduralAudio", "HandleCreateProceduralBuildingGenerator", "HandleCreateProceduralRoadNetwork", "HandleCreateProceduralTerrainSystem", "HandleCreateQuestObjectiveGenerator", "HandleCreateQuestRewardGenerator", "HandleCreateRagdollPhysics", "HandleCreateReverbZones", "HandleCreateRigidbodySystem", "HandleCreateRopePhysics", "HandleCreateSessionPersistence", "HandleCreateShaderVariantCollection", "HandleCreateSimulatedEcosystem", "HandleCreateSpatialAnchors", "HandleCreateTeleportationSystem", "HandleCreateTerrain", "HandleCreateTerrainDetails", "HandleCreateTerrainTrees", "HandleCreateTextureAtlas", "HandleCreateTextureVariationsAi", "HandleCreateTrailEffects", "HandleCreateTriggerSystems", "HandleCreateUiDataBinding", "HandleCreateUiEffects", "HandleCreateUvMappings", "HandleCreateVfxGarbageCollectionMonitor", "HandleCreateVfxParameterController", "HandleCreateVolumetricLighting", "HandleCreateVrPlayerRig", "HandleCreateVrsQualityRegions", "HandleCreateWaterCausticsSystem", "HandleCreateWaterFoamSystem", "HandleCreateWaterLineTransition", "HandleCreateWaterSystem", "HandleCreateWeatherEffects", "HandleCreateWebgpuVfxSystem", "HandleCreateXrAnalytics", "HandleCreateXrInputSystem", "HandleCreateXrUiSystem", "HandleCullRaytracingInstances", "HandleDynamicRealmSystem", "HandleGenerate2dLevelLayout", "HandleGenerateActionNodesFromDescription", "HandleGenerateAiAnimation", "HandleGenerateAiBehaviourTree", "HandleGenerateAiMaterial", "HandleGenerateAiSound", "HandleGenerateAiSprite", "HandleGenerateAiTexture", "HandleGenerateBehaviorNodesWithAi", "HandleGenerateBranchingDialogueTree", "HandleGenerateCodeWithAssistant", "HandleGenerateDynamicPointsOfInterest", "HandleGenerateEnemyStatCurves", "HandleGenerateGameplayRulesEngine", "HandleGenerateItemDatabaseProcedural", "HandleGenerateLSystemStructures", "HandleGenerateLodMeshes", "HandleGenerateMaterialFromTextureAi", "HandleGenerateMeshFromHeightmap", "HandleGenerateNpcBackstory", "HandleGenerateParticleSystem", "HandleGenerateProceduralMesh", "HandleGenerateQuestStateMachine", "HandleGenerateTilemapFromSet", "HandleGenerateTimelineFromEvents", "HandleGetConnectedPlayers", "HandleGetMultiplayerStatus", "HandleJoinMultiplayerLobby", "HandleLightmapEdgeSmoothing", "HandleLightmapPerformanceAnalysis", "HandleLightmapQualitySettings", "HandleLightmapVisualizationTools", "HandleLinkQuestsToGameplayEvents", "HandleManageAiPointsSystem", "HandleManageBehaviorGraph", "HandleManageEditor", "HandleManageGameobject", "HandleManageScene", "HandleManageScript", "HandleMonitorAiUsageAnalytics", "HandleMonitorNetworkPerformance", "HandleMonitorRuntimePerformance", "HandleOneClickCharacterCreator", "HandleOptimizeAssetPipeline", "HandleOptimizeAssets", "HandleOptimizeAudioPerformance", "HandleOptimizeBandwidthUsage", "HandleOptimizeBehaviorTreeWithAi", "HandleOptimizeBuildSize", "HandleOptimizeExistingCode", "HandleOptimizeGenerativeTilemap", "HandleOptimizeInference", "HandleOptimizeMemoryUsage", "HandleOptimizePhysicsPerformance", "HandleOptimizeProceduralGeometry", "HandleOptimizeRenderingPerformance", "HandleOptimizeRenderingPipeline", "HandleOptimizeTerrainPerformance", "HandleOptimizeVfxPerformance", "HandlePaintTerrainTextures", "HandleProceduralAnimationGeneration", "HandleProceduralBodyPartGeneration", "HandleProceduralSkeletonGeneration", "HandleProceduralTextureGeneration", "HandleProjectAnalysisWithAssistant", "HandleReadConsole", "HandleRefactorCodeStructure", "HandleRefineAiGeneratedAssets", "HandleRemoveRaytracingInstance", "HandleRunUnityTestSuite", "HandleSceneEditingWithAssistant", "HandleSendMultiplayerMessage", "HandleSequenceGeneratedAnimationsOnTimeline", "HandleSessionMatchmaking", "HandleSessionMigration", "HandleSessionPersistence", "HandleSetup2dAnimationRuntime", "HandleSetup3dWaterDeformation", "HandleSetupAdaptiveDifficulty", "HandleSetupAiAssetCloudSync", "HandleSetupAiAssetPipeline", "HandleSetupAiAssistant", "HandleSetupAiCommunication", "HandleSetupAiOptimization", "HandleSetupAssetDependencyAnalysis", "HandleSetupAudioCompression", "HandleSetupAudioDspTimeSync", "HandleSetupAudioFilters", "HandleSetupAudioFormatConversion", "HandleSetupAudioMixerSnapshotBlending", "HandleSetupAudioOcclusion", "HandleSetupAudioStreaming", "HandleSetupAudioVirtualizationRuntime", "HandleSetupAutomatedBuildPipeline", "HandleSetupBicubicLightmapSampling", "HandleSetupBinauralAudio", "HandleSetupBloomEffects", "HandleSetupBuffDebuffSystem", "HandleSetupBuoyancySystem", "HandleSetupCinemachineProceduralCamera", "HandleSetupCodeAnalysisRuntime", "HandleSetupCollisionLayers", "HandleSetupComfortSettings", "HandleSetupComputeSkinning", "HandleSetupCrossPlatformXr", "HandleSetupCrowdSimulation", "HandleSetupCustomBackendDispatching", "HandleSetupDedicatedServers", "HandleSetupDeferredPlusDebugViews", "HandleSetupDeferredPlusLighting", "HandleSetupDeferredPlusPipeline", "HandleSetupDeferredPlusShaderVariants", "HandleSetupDepthOfField", "HandleSetupDistortionEffects", "HandleSetupDomainReloadAnalysis", "HandleSetupDomainReloadMonitoring", "HandleSetupDynamicEventSpawner", "HandleSetupDynamicLoreSystem", "HandleSetupDynamicNavmeshAreas", "HandleSetupEnvironmentZones", "HandleSetupEnvironmentalEffects", "HandleSetupEyeTracking", "HandleSetupFluidSimulation", "HandleSetupFogEffects", "HandleSetupFrameSlicing", "HandleSetupGenerativeAiBehaviorGraph", "HandleSetupGeometryScriptingRuntime", "HandleSetupGlobalIllumination", "HandleSetupGraphicsOverrideRuntime", "HandleSetupGraphicsStateCollection", "HandleSetupGraphicsStateSerialization", "HandleSetupGraphicsStateTracing", "HandleSetupHandTracking", "HandleSetupHapticFeedback", "HandleSetupHdrPipeline", "HandleSetupIkSystems", "HandleSetupInferenceAsyncInference", "HandleSetupInferenceBackendSelection", "HandleSetupInferenceDialogueGenerator", "HandleSetupInferenceFrameSlicing", "HandleSetupInferenceMemoryOptimization", "HandleSetupInferenceRuntime", "HandleSetupInputDeviceManagement", "HandleSetupInputEventRouting", "HandleSetupInputSystemRuntime", "HandleSetupJointSystems", "HandleSetupLightShafts", "HandleSetupLightmapEdgeSmoothing", "HandleSetupLightmapPerformanceAnalysis", "HandleSetupMachineLearning", "HandleSetupMagicEffects", "HandleSetupMotionCapture", "HandleSetupMotionMatching", "HandleSetupMultiplayerSessions", "HandleSetupNavmeshLinks", "HandleSetupNavmeshSystem", "HandleSetupNetworkCulling", "HandleSetupObjectRecognition", "HandleSetupPassthroughAr", "HandleSetupPathfinding", "HandleSetupPerformanceBudgets", "HandleSetupPhysicsDebugVisualization", "HandleSetupPhysicsLayersRuntime", "HandleSetupPhysicsMaterials", "HandleSetupPhysicsOptimization", "HandleSetupPipelineStateTracing", "HandleSetupPlatformSpecificSettings", "HandleSetupProceduralCaves", "HandleSetupProceduralEventSystem", "HandleSetupProceduralLootTables", "HandleSetupProceduralVegetation", "HandleSetupProjectAuditor", "HandleSetupProjectAuditorIntegration", "HandleSetupProjectDependencies", "HandleSetupRealtimeGi", "HandleSetupReflectionProbes", "HandleSetupRoomScaleTracking", "HandleSetupRuntimeAssetOverrides", "HandleSetupRuntimeObstacleAvoidance", "HandleSetupScreenEffects", "HandleSetupScreenSpaceReflections", "HandleSetupScriptSaveLocationHandler", "HandleSetupSessionMatchmaking", "HandleSetupShaderCompilationMonitoring", "HandleSetupShaderVariantAnalysis", "HandleSetupShaderVariantCollection", "HandleSetupShaderVariantReporting", "HandleSetupShadowCascades", "HandleSetupSoftBodyPhysics", "HandleSetupSpatialAudio", "HandleSetupSpriteAtlasRuntime", "HandleSetupTerrainCollision", "HandleSetupTerrainGrass", "HandleSetupTileSetVariations", "HandleSetupTilemapGeneration", "HandleSetupTimelineSequences", "HandleSetupUiProfilerMarkers", "HandleSetupUiRuntimeEvents", "HandleSetupUiToolkitAnimations", "HandleSetupUiToolkitRuntime", "HandleSetupUnityAi", "HandleSetupUnityCloudAiProject", "HandleSetupUnityGamingServices", "HandleSetupVariableRateShading", "HandleSetupVehiclePhysics", "HandleSetupVersionControlIntegration", "HandleSetupVfxGraph", "HandleSetupVfxGraphShaderBinding", "HandleSetupVfxParallelizationTuning", "HandleSetupVfxParticleDataLayout", "HandleSetupVfxRuntimeRecompilation", "HandleSetupVoiceChat", "HandleSetupVrsApiIntegration", "HandleSetupVrsCustomPasses", "HandleSetupVrsPerformanceScaling", "HandleSetupWaterCausticsBuffer", "HandleSetupWaterEffects", "HandleSetupWaterFlowMaps", "HandleSetupWaterQueryApi", "HandleSetupWeatherSystem", "HandleSetupWebgpuCompatibilityDetection", "HandleSetupWebgpuComputeShaders", "HandleSetupWebgpuGpuSkinning", "HandleSetupWindSystem", "HandleSetupWorldStateChangeSystem", "HandleSetupXrPerformanceOptimization", "HandleSetupXrToolkit", "HandleSimulateNpcDailyRoutines", "HandleSimulateSupplyAndDemand", "HandleStartMultiplayerHost", "HandleSyncPlayerData", "HandleTriggerCinematicFromGameplay", "HandleTroubleshootWithAssistant", "HandleUnityCloudTesting", "HandleUnityTestRunner", "HandleUpdateAccelerationStructure", "HandleUpdateRaytracingGeometry", "HandleValidateGeneratedBehaviorCode", "HandleValidateProjectIntegrity"], "csharp_handlers": ["HandleAIAdaptiveJungle", "HandleAIAssetGeneration", "HandleAIBehaviourTree", "HandleAIRuntime", "HandleAdvancedAI", "HandleAdvancedAudio", "HandleAdvancedPhysics", "HandleAdvancedRendering", "HandleAnalyzeGameplayDataForBalancing", "HandleAnimationRuntime", "HandleAssetProcessing", "HandleAttachBehaviourScript", "HandleAudioSystem", "HandleAutomatedCinematics", "HandleBalanceSkillCostsAndEffects", "HandleBehaviorAIGeneration", "HandleBuffDebuffSystem", "HandleBuildProfilesRuntime", "HandleChampionFusionSystem", "HandleCharacterGameplaySystem", "HandleConfigureAssetUsageAnalysis", "HandleConfigureBuildSizeAnalysis", "HandleConfigureInferenceOnnxImport", "HandleConfigureInferenceTensorOperations", "HandleConfigureShaderGraphVfxSupport", "HandleConfigureShadingRateImage", "HandleConfigureVfxGpuInstancing", "HandleConfigureVrsDebugVisualization", "HandleCreateDynamicShopInventory", "HandleCreateEconomyEventSimulator", "HandleCreateInferenceModelVisualization", "HandleCreateInferenceQuantizationSystem", "HandleCreateMemoryProfilingReport", "HandleCreatePerformanceRecommendations", "HandleCreateProceduralTerrainSystem", "HandleCreateVfxGarbageCollectionMonitor", "HandleCreateVfxParameterController", "HandleCreateVrsQualityRegions", "HandleDeferredPlusRendering", "HandleDistributedAuthority", "HandleDynamicGameSystems", "HandleDynamicRealmSystem", "HandleDynamicSkillTree", "HandleEditorBuildTools", "HandleExecuteMenuItem", "HandleGameStateManager", "HandleGameplayRulesEngine", "HandleGenerateEnemyStatCurves", "HandleGenerateItemDatabaseProcedural", "HandleGenerateMeshFromHeightmap", "HandleGenerateProceduralMesh", "HandleGenerative2DWorld", "HandleGenerativeStateMachine", "HandleGraphicsStateCollection", "HandleInferenceNeuralNetwork", "HandleLightingRendering", "HandleLightmapEdgeSmoothing", "HandleLightmapPerformanceAnalysis", "HandleLightmapQualitySettings", "HandleLightmapVisualizationTools", "HandleManageAsset", "HandleManageEditor", "HandleManageGameObject", "HandleManageScene", "HandleManageScript", "HandleMultiplayerNetworking", "HandleMultiplayerSessions", "HandleNavigationEnhanced", "HandleObjectiveTrackerSystem", "HandleOneClickCharacterCreator", "HandleProceduralAnimationGeneration", "HandleProceduralBodyPartGeneration", "HandleProceduralCharacterAssembly", "HandleProceduralEventSystem", "HandleProceduralNarrative", "HandleProceduralObjectives", "HandleProceduralSkeletonGeneration", "HandleProceduralTextureGeneration", "HandleProjectAuditorRuntime", "HandleProjectCodeOperations", "HandleRaytracingOperations", "HandleReadConsole", "HandleSessionMatchmaking", "HandleSessionMigration", "HandleSessionPersistence", "HandleSetupCodeAnalysisRuntime", "HandleSetupDomainReloadMonitoring", "HandleSetupInferenceAsyncInference", "HandleSetupInferenceBackendSelection", "HandleSetupInferenceFrameSlicing", "HandleSetupInferenceMemoryOptimization", "HandleSetupProceduralLootTables", "HandleSetupProceduralVegetation", "HandleSetupProjectAuditorIntegration", "HandleSetupShaderVariantReporting", "HandleSetupVfxGraphShaderBinding", "HandleSetupVfxParallelizationTuning", "HandleSetupVfxParticleDataLayout", "HandleSetupVfxRuntimeRecompilation", "HandleSetupVrsApiIntegration", "HandleSetupVrsCustomPasses", "HandleSetupVrsPerformanceScaling", "HandleSimulateSupplyAndDemand", "HandleTerrainEnvironment", "HandleTwoDToolkitRuntime", "HandleUIToolkitRuntime", "HandleUnityAIAssistant", "HandleUnityCloudAI", "HandleUnityCloudTesting", "HandleUnityTestRunner", "HandleUnityTesting", "HandleWaterSystemRuntime", "HandleWebGPUCompatibilityDetection", "HandleWebGPUGPUSkinning", "HandleWebGPUIndirectRendering", "HandleWebGPURuntime", "HandleWebGPUVFXSystem", "HandleWorldSimulationSystems", "HandleXRRuntime"]}