#!/usr/bin/env python3
"""
Script para testar quais ferramentas MCP estão realmente disponíveis
"""
import json
import sys
import importlib.util
import inspect
from pathlib import Path

def find_mcp_tools():
    """Encontra todas as ferramentas MCP registradas."""
    tools_found = []
    
    # Carregar o inventário
    try:
        with open('mcp_tools_inventory.json', 'r', encoding='utf-8') as f:
            inventory = json.load(f)
    except FileNotFoundError:
        print("❌ Arquivo mcp_tools_inventory.json não encontrado!")
        return []
    
    print("🔍 Verificando ferramentas MCP disponíveis...")
    
    # Verificar cada ferramenta no inventário
    for tool in inventory['tools']:
        tool_name = tool['name']
        file_name = tool['file']
        
        # Tentar importar o módulo
        try:
            module_path = f"UnityMcpServer/src/tools/{file_name}"
            if Path(module_path).exists():
                # Verificar se a função existe
                spec = importlib.util.spec_from_file_location(file_name.replace('.py', ''), module_path)
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    
                    # Procurar pela função
                    if hasattr(module, tool_name):
                        tools_found.append({
                            'name': tool_name,
                            'file': file_name,
                            'status': 'available'
                        })
                        print(f"  ✅ {tool_name} - Disponível")
                    else:
                        tools_found.append({
                            'name': tool_name,
                            'file': file_name,
                            'status': 'function_not_found'
                        })
                        print(f"  ❌ {tool_name} - Função não encontrada")
                else:
                    tools_found.append({
                        'name': tool_name,
                        'file': file_name,
                        'status': 'module_load_error'
                    })
                    print(f"  ❌ {tool_name} - Erro ao carregar módulo")
            else:
                tools_found.append({
                    'name': tool_name,
                    'file': file_name,
                    'status': 'file_not_found'
                })
                print(f"  ❌ {tool_name} - Arquivo não encontrado")
                
        except Exception as e:
            tools_found.append({
                'name': tool_name,
                'file': file_name,
                'status': 'error',
                'error': str(e)
            })
            print(f"  ❌ {tool_name} - Erro: {e}")
    
    return tools_found

def analyze_results(tools_found):
    """Analisa os resultados dos testes."""
    total = len(tools_found)
    available = len([t for t in tools_found if t['status'] == 'available'])
    
    print(f"\n📊 RESULTADOS:")
    print(f"  Total de ferramentas: {total}")
    print(f"  Disponíveis: {available}")
    print(f"  Taxa de disponibilidade: {(available/total)*100:.1f}%")
    
    # Agrupar por status
    status_groups = {}
    for tool in tools_found:
        status = tool['status']
        if status not in status_groups:
            status_groups[status] = []
        status_groups[status].append(tool['name'])
    
    print(f"\n📋 DETALHAMENTO POR STATUS:")
    for status, tools in status_groups.items():
        print(f"  {status}: {len(tools)} ferramentas")
        if len(tools) <= 10:  # Mostrar apenas se não for muitas
            for tool in tools[:5]:  # Mostrar apenas as primeiras 5
                print(f"    - {tool}")
            if len(tools) > 5:
                print(f"    ... e mais {len(tools) - 5}")
    
    # Salvar resultados
    with open('mcp_tools_availability.json', 'w', encoding='utf-8') as f:
        json.dump({
            'total_tools': total,
            'available_tools': available,
            'availability_rate': (available/total)*100,
            'tools': tools_found,
            'status_summary': {status: len(tools) for status, tools in status_groups.items()}
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Resultados salvos em: mcp_tools_availability.json")

def main():
    """Função principal."""
    print("🚀 Testando disponibilidade de ferramentas MCP...")
    
    tools_found = find_mcp_tools()
    analyze_results(tools_found)

if __name__ == "__main__":
    main()
