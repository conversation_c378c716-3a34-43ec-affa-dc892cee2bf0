%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: ProceduralLeg
  serializedVersion: 12
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 72
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 26
    localAABB:
      m_Center: {x: 0, y: 0.45, z: 0}
      m_Extent: {x: 0.08, y: 0.45, z: 0.08}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 00000d00010001000d000e0001000e00020002000e000f0002000f00030003000f001000030010000400040010001100040011000500050011001200050012000600060012001300060013000700070013001400070014000800080014001500080015000900090015001600090016000a000a00160017000a0017000b000b00170018000b0018000c000c0018001900
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 26
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 832
    _typelessdata: 0ad7a33d0000000000000000eb46773f00000000ed83843e0000000000000000bde38d3d000000000ad7233deb35683f00000000e589d73eabaaaa3d0000000009d7233d00000000bde38d3d3337133f00000000b06f513fabaa2a3e000000004f4e70b1000000000ad7a33dd631b63d0000000026fc7e3f0000803e000000000bd723bd00000000bde38d3de389d7be00000000eb35683fabaaaa3e00000000bde38dbd000000000bd7233db06f51bf000000003237133f5555d53e000000000ad7a3bd000000004f4ef0b127fc7ebf00000000c331b63d0000003f00000000bbe38dbd000000000ed723bdea3568bf00000000e989d7be5555153f0000000008d723bd00000000bde38dbd323713bf00000000b16f51bfabaa2a3f000000008e1d8330000000000ad7a3bdd831b6bd0000000026fc7ebf0000403f0000000008d7233d00000000bde38dbde889d73e00000000eb3568bf5555553f00000000bfe38d3d0000000005d723bdaf6f513f00000000343713bfabaa6a3f000000000ad7a33d000000004f4e7032eb46773f00000000e28384be0000803f000000000ad7a33d6666663f00000000eb46773f00000000ed83843e000000000000803fbde38d3d6666663f0ad7233db06f513f000000003537133fabaaaa3d0000803f09d7233d6666663fbde38d3de589d73e00000000ec35683fabaa2a3e0000803f4f4e70b16666663f0ad7a33dd631b6bd0000000026fc7e3f0000803e0000803f0bd723bd6666663fbde38d3d333713bf00000000b06f513fabaaaa3e0000803fbde38dbd6666663f0bd7233dec3568bf00000000e089d73e5555d53e0000803f0ad7a3bd6666663f4f4ef0b126fc7ebf00000000f431b6bd0000003f0000803fbbe38dbd6666663f0ed723bdb06f51bf00000000363713bf5555153f0000803f08d723bd6666663fbde38dbde389d7be00000000eb3568bfabaa2a3f0000803f8e1d83306666663f0ad7a3bdd831b63d0000000026fc7ebf0000403f0000803f08d7233d6666663fbde38dbd3437133f00000000b06f51bf5555553f0000803fbfe38d3d6666663f05d723bdec35683f00000000e289d7beabaa6a3f0000803f0ad7a33d6666663f4f4e7032eb46773f00000000e28384be0000803f0000803f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.45, z: 0}
    m_Extent: {x: 0.08, y: 0.45, z: 0.08}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
  m_MeshLodInfo:
    serializedVersion: 2
    m_LodSelectionCurve:
      serializedVersion: 1
      m_LodSlope: 0
      m_LodBias: 0
    m_NumLevels: 1
    m_SubMeshes:
    - serializedVersion: 2
      m_Levels:
      - serializedVersion: 1
        m_IndexStart: 0
        m_IndexCount: 0
