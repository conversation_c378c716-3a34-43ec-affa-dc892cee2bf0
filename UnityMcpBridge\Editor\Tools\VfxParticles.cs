using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.VFX;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine.UI;

namespace UnityMcpBridge.Editor.Tools
{
    public static class JsonHelper
    {
        public static string CreateSuccessResponse(string message, object data = null)
        {
            var response = new JObject
            {
                ["success"] = true,
                ["message"] = message
            };
            
            if (data != null)
            {
                response["data"] = JToken.FromObject(data);
            }
            
            return response.ToString();
        }
        
        public static string CreateErrorResponse(string message)
        {
            var response = new JObject
            {
                ["success"] = false,
                ["error"] = message
            };
            
            return response.ToString();
        }
        
        public static Vector3 ParseVector3(JToken token)
        {
            return ParseVector3(token, Vector3.zero);
        }
        
        public static Vector3 ParseVector3(JToken token, Vector3 defaultValue)
        {
            if (token == null) return defaultValue;
            
            if (token.Type == JTokenType.Array)
            {
                var array = token as JArray;
                if (array.Count >= 3)
                {
                    return new Vector3(
                        array[0].ToObject<float>(),
                        array[1].ToObject<float>(),
                        array[2].ToObject<float>()
                    );
                }
            }
            else if (token.Type == JTokenType.Object)
            {
                var obj = token as JObject;
                return new Vector3(
                    obj["x"]?.ToObject<float>() ?? defaultValue.x,
                    obj["y"]?.ToObject<float>() ?? defaultValue.y,
                    obj["z"]?.ToObject<float>() ?? defaultValue.z
                );
            }
            
            return defaultValue;
        }
    }

    public static class VfxParticles
    {
        public static string HandleCommand(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                if (string.IsNullOrEmpty(action))
                {
                    return JsonHelper.CreateErrorResponse("Action parameter is required");
                }

                return action.ToLower() switch
                {
                    "create_particle_systems" => CreateParticleSystems(parameters),
                    "setup_vfx_graph" => SetupVfxGraph(parameters),
                    "create_fire_effects" => CreateFireEffects(parameters),
                    "setup_water_effects" => SetupWaterEffects(parameters),
                    "create_explosion_effects" => CreateExplosionEffects(parameters),
                    "setup_magic_effects" => SetupMagicEffects(parameters),
                    "create_weather_effects" => CreateWeatherEffects(parameters),
                    "setup_environmental_effects" => SetupEnvironmentalEffects(parameters),
                    "create_ui_effects" => CreateUiEffects(parameters),
                    "setup_screen_effects" => SetupScreenEffects(parameters),
                    "create_trail_effects" => CreateTrailEffects(parameters),
                    "setup_distortion_effects" => SetupDistortionEffects(parameters),
                    "create_hologram_effects" => CreateHologramEffects(parameters),
                    "optimize_vfx_performance" => OptimizeVfxPerformance(parameters),
                    _ => JsonHelper.CreateErrorResponse($"Unknown action: {action}")
                };
            }
            catch (Exception ex)
            {
                Debug.LogError($"VfxParticles HandleCommand error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error handling VFX command: {ex.Message}");
            }
        }

        private static string CreateParticleSystems(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();

                switch (action?.ToLower())
                {
                    case "create":
                        return CreateParticleSystem(parameters);
                    case "modify":
                        return ModifyParticleSystem(parameters);
                    case "delete":
                        return DeleteParticleSystem(gameObjectName);
                    case "get_properties":
                        return GetParticleSystemProperties(gameObjectName);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown particle system action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateParticleSystems error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error with particle systems: {ex.Message}");
            }
        }

        private static string CreateParticleSystem(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString() ?? "ParticleSystem";
                
                GameObject go = new GameObject(gameObjectName);
                ParticleSystem ps = go.AddComponent<ParticleSystem>();
                
                var main = ps.main;
                var emission = ps.emission;
                var shape = ps.shape;
                var velocityOverLifetime = ps.velocityOverLifetime;
                var sizeOverLifetime = ps.sizeOverLifetime;
                var colorOverLifetime = ps.colorOverLifetime;
                var textureSheetAnimation = ps.textureSheetAnimation;
                var collision = ps.collision;
                var subEmitters = ps.subEmitters;
                var noise = ps.noise;
                var trails = ps.trails;
                var renderer = ps.GetComponent<ParticleSystemRenderer>();

                // Configure main module
                if (parameters["emission_rate"] != null)
                {
                    emission.rateOverTime = parameters["emission_rate"].Value<float>();
                }
                if (parameters["max_particles"] != null)
                {
                    main.maxParticles = parameters["max_particles"].Value<int>();
                }
                if (parameters["start_lifetime"] != null)
                {
                    main.startLifetime = parameters["start_lifetime"].Value<float>();
                }
                if (parameters["start_speed"] != null)
                {
                    main.startSpeed = parameters["start_speed"].Value<float>();
                }
                if (parameters["start_size"] != null)
                {
                    main.startSize = parameters["start_size"].Value<float>();
                }
                if (parameters["start_color"] != null)
                {
                    var colorArray = parameters["start_color"].ToObject<float[]>();
                    if (colorArray.Length >= 4)
                    {
                        main.startColor = new Color(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);
                    }
                }

                // Configure shape
                if (parameters["shape_type"] != null)
                {
                    string shapeType = parameters["shape_type"].ToString();
                    shape.enabled = true;
                    shape.shapeType = shapeType.ToLower() switch
                    {
                        "sphere" => ParticleSystemShapeType.Sphere,
                        "box" => ParticleSystemShapeType.Box,
                        "circle" => ParticleSystemShapeType.Circle,
                        "cone" => ParticleSystemShapeType.Cone,
                        "edge" => ParticleSystemShapeType.SingleSidedEdge, // Fixed: Edge -> SingleSidedEdge
                        _ => ParticleSystemShapeType.Sphere
                    };
                    
                    if (parameters["shape_radius"] != null)
                    {
                        shape.radius = parameters["shape_radius"].Value<float>();
                    }
                }

                // Configure velocity over lifetime
                if (parameters["velocity_over_lifetime"] != null)
                {
                    var velocitySettings = parameters["velocity_over_lifetime"].ToObject<Dictionary<string, object>>();
                    velocityOverLifetime.enabled = true;
                    
                    if (velocitySettings.ContainsKey("linear"))
                    {
                        var linear = ((JArray)velocitySettings["linear"]).ToObject<float[]>();
                        if (linear.Length >= 3)
                        {
                            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
                            velocityOverLifetime.x = linear[0];
                            velocityOverLifetime.y = linear[1];
                            velocityOverLifetime.z = linear[2];
                        }
                    }
                }

                // Configure size over lifetime
                if (parameters["size_over_lifetime"] != null)
                {
                    var sizeSettings = parameters["size_over_lifetime"].ToObject<Dictionary<string, object>>();
                    sizeOverLifetime.enabled = true;
                    
                    if (sizeSettings.ContainsKey("curve"))
                    {
                        var curveData = ((JArray)sizeSettings["curve"]).ToObject<float[]>();
                        AnimationCurve curve = new AnimationCurve();
                        for (int i = 0; i < curveData.Length - 1; i += 2)
                        {
                            curve.AddKey(curveData[i], curveData[i + 1]);
                        }
                        sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, curve); // Fixed: Convert AnimationCurve to MinMaxCurve
                    }
                }

                // Configure color over lifetime
                if (parameters["color_over_lifetime"] != null)
                {
                    var colorSettings = parameters["color_over_lifetime"].ToObject<Dictionary<string, object>>();
                    colorOverLifetime.enabled = true;
                    
                    if (colorSettings.ContainsKey("gradient"))
                    {
                        var gradientData = ((JArray)colorSettings["gradient"]).ToObject<Dictionary<string, object>[]>();
                        Gradient gradient = new Gradient();
                        List<GradientColorKey> colorKeys = new List<GradientColorKey>();
                        List<GradientAlphaKey> alphaKeys = new List<GradientAlphaKey>();
                        
                        foreach (var keyData in gradientData)
                        {
                            float time = Convert.ToSingle(keyData["time"]);
                            var colorArray = ((JArray)keyData["color"]).ToObject<float[]>();
                            
                            if (colorArray.Length >= 4)
                            {
                                colorKeys.Add(new GradientColorKey(new Color(colorArray[0], colorArray[1], colorArray[2]), time));
                                alphaKeys.Add(new GradientAlphaKey(colorArray[3], time));
                            }
                        }
                        
                        gradient.SetKeys(colorKeys.ToArray(), alphaKeys.ToArray());
                        colorOverLifetime.color = gradient;
                    }
                }

                // Configure texture sheet animation
                if (parameters["texture_sheet_animation"] != null)
                {
                    var animSettings = parameters["texture_sheet_animation"].ToObject<Dictionary<string, object>>();
                    textureSheetAnimation.enabled = true;
                    
                    if (animSettings.ContainsKey("tiles"))
                    {
                        var tiles = ((JArray)animSettings["tiles"]).ToObject<int[]>();
                        if (tiles.Length >= 2)
                        {
                            textureSheetAnimation.numTilesX = tiles[0];
                            textureSheetAnimation.numTilesY = tiles[1];
                        }
                    }
                    
                    if (animSettings.ContainsKey("animation_type"))
                    {
                        string animType = animSettings["animation_type"].ToString();
                        textureSheetAnimation.animation = animType.ToLower() switch
                        {
                            "whole_sheet" => ParticleSystemAnimationType.WholeSheet,
                            "single_row" => ParticleSystemAnimationType.SingleRow,
                            _ => ParticleSystemAnimationType.WholeSheet
                        };
                    }
                }

                // Configure collision
                if (parameters["collision_settings"] != null)
                {
                    var collisionSettings = parameters["collision_settings"].ToObject<Dictionary<string, object>>();
                    collision.enabled = true;
                    
                    if (collisionSettings.ContainsKey("type"))
                    {
                        string collisionType = collisionSettings["type"].ToString();
                        collision.type = collisionType.ToLower() switch
                        {
                            "planes" => ParticleSystemCollisionType.Planes,
                            "world" => ParticleSystemCollisionType.World,
                            _ => ParticleSystemCollisionType.Planes
                        };
                    }
                    
                    if (collisionSettings.ContainsKey("bounce"))
                    {
                        collision.bounce = Convert.ToSingle(collisionSettings["bounce"]);
                    }
                    
                    if (collisionSettings.ContainsKey("dampen"))
                    {
                        collision.dampen = Convert.ToSingle(collisionSettings["dampen"]);
                    }
                }

                // Configure sub emitters
                if (parameters["sub_emitters"] != null)
                {
                    var subEmitterSettings = parameters["sub_emitters"].ToObject<Dictionary<string, object>[]>();
                    subEmitters.enabled = true;
                    
                    for (int i = 0; i < subEmitterSettings.Length && i < subEmitters.subEmittersCount; i++)
                    {
                        var settings = subEmitterSettings[i];
                        if (settings.ContainsKey("type"))
                        {
                            string subType = settings["type"].ToString();
                            ParticleSystemSubEmitterType emitterType = subType.ToLower() switch
                            {
                                "birth" => ParticleSystemSubEmitterType.Birth,
                                "death" => ParticleSystemSubEmitterType.Death,
                                "collision" => ParticleSystemSubEmitterType.Collision,
                                _ => ParticleSystemSubEmitterType.Birth
                            };
                            subEmitters.SetSubEmitterType(i, emitterType);
                        }
                    }
                }

                // Configure noise
                if (parameters["noise_settings"] != null)
                {
                    var noiseSettings = parameters["noise_settings"].ToObject<Dictionary<string, object>>();
                    noise.enabled = true;
                    
                    if (noiseSettings.ContainsKey("strength"))
                    {
                        noise.strength = Convert.ToSingle(noiseSettings["strength"]);
                    }
                    
                    if (noiseSettings.ContainsKey("frequency"))
                    {
                        noise.frequency = Convert.ToSingle(noiseSettings["frequency"]);
                    }
                    
                    if (noiseSettings.ContainsKey("scroll_speed"))
                    {
                        noise.scrollSpeed = Convert.ToSingle(noiseSettings["scroll_speed"]);
                    }
                }

                // Configure trails
                if (parameters["trails_settings"] != null)
                {
                    var trailSettings = parameters["trails_settings"].ToObject<Dictionary<string, object>>();
                    trails.enabled = true;
                    
                    if (trailSettings.ContainsKey("ratio"))
                    {
                        trails.ratio = Convert.ToSingle(trailSettings["ratio"]);
                    }
                    
                    if (trailSettings.ContainsKey("lifetime"))
                    {
                        trails.lifetime = Convert.ToSingle(trailSettings["lifetime"]);
                    }
                    
                    if (trailSettings.ContainsKey("width_over_trail"))
                    {
                        trails.widthOverTrail = Convert.ToSingle(trailSettings["width_over_trail"]);
                    }
                }

                // Configure renderer
                if (parameters["renderer_settings"] != null)
                {
                    var rendererSettings = parameters["renderer_settings"].ToObject<Dictionary<string, object>>();
                    
                    if (rendererSettings.ContainsKey("material_path"))
                    {
                        string materialPath = rendererSettings["material_path"].ToString();
                        Material material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                        if (material != null)
                        {
                            renderer.material = material;
                        }
                    }
                    
                    if (rendererSettings.ContainsKey("render_mode"))
                    {
                        string renderMode = rendererSettings["render_mode"].ToString();
                        renderer.renderMode = renderMode.ToLower() switch
                        {
                            "billboard" => ParticleSystemRenderMode.Billboard,
                            "stretched_billboard" => ParticleSystemRenderMode.Stretch, // Fixed: StretchedBillboard -> Stretch
                            "horizontal_billboard" => ParticleSystemRenderMode.HorizontalBillboard,
                            "vertical_billboard" => ParticleSystemRenderMode.VerticalBillboard,
                            "mesh" => ParticleSystemRenderMode.Mesh,
                            _ => ParticleSystemRenderMode.Billboard
                        };
                    }
                }

                Selection.activeGameObject = go;
                
                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = go.name,
                    ["instanceId"] = go.GetInstanceID(),
                    ["particleCount"] = ps.particleCount,
                    ["isPlaying"] = ps.isPlaying
                };

                return JsonHelper.CreateSuccessResponse("Particle system created successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateParticleSystem error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating particle system: {ex.Message}");
            }
        }

        private static string ModifyParticleSystem(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for modification");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                ParticleSystem ps = go.GetComponent<ParticleSystem>();
                if (ps == null)
                {
                    return JsonHelper.CreateErrorResponse($"ParticleSystem component not found on '{gameObjectName}'");
                }

                // Apply modifications using the same logic as CreateParticleSystem
                // This allows for runtime modification of existing particle systems
                var main = ps.main;
                var emission = ps.emission;
                
                if (parameters["emission_rate"] != null)
                {
                    emission.rateOverTime = parameters["emission_rate"].Value<float>();
                }
                if (parameters["max_particles"] != null)
                {
                    main.maxParticles = parameters["max_particles"].Value<int>();
                }
                if (parameters["start_lifetime"] != null)
                {
                    main.startLifetime = parameters["start_lifetime"].Value<float>();
                }
                if (parameters["start_speed"] != null)
                {
                    main.startSpeed = parameters["start_speed"].Value<float>();
                }
                if (parameters["start_size"] != null)
                {
                    main.startSize = parameters["start_size"].Value<float>();
                }
                if (parameters["start_color"] != null)
                {
                    var colorArray = parameters["start_color"].ToObject<float[]>();
                    if (colorArray.Length >= 4)
                    {
                        main.startColor = new Color(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);
                    }
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = go.name,
                    ["particleCount"] = ps.particleCount,
                    ["isPlaying"] = ps.isPlaying
                };

                return JsonHelper.CreateSuccessResponse("Particle system modified successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"ModifyParticleSystem error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error modifying particle system: {ex.Message}");
            }
        }

        private static string DeleteParticleSystem(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for deletion");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                ParticleSystem ps = go.GetComponent<ParticleSystem>();
                if (ps == null)
                {
                    return JsonHelper.CreateErrorResponse($"ParticleSystem component not found on '{gameObjectName}'");
                }

                UnityEngine.Object.DestroyImmediate(go);
                
                return JsonHelper.CreateSuccessResponse($"Particle system '{gameObjectName}' deleted successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"DeleteParticleSystem error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error deleting particle system: {ex.Message}");
            }
        }

        private static string GetParticleSystemProperties(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                ParticleSystem ps = go.GetComponent<ParticleSystem>();
                if (ps == null)
                {
                    return JsonHelper.CreateErrorResponse($"ParticleSystem component not found on '{gameObjectName}'");
                }

                var main = ps.main;
                var emission = ps.emission;
                var shape = ps.shape;
                
                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = go.name,
                    ["isPlaying"] = ps.isPlaying,
                    ["isPaused"] = ps.isPaused,
                    ["isStopped"] = ps.isStopped,
                    ["particleCount"] = ps.particleCount,
                    ["main"] = new Dictionary<string, object>
                    {
                        ["maxParticles"] = main.maxParticles,
                        ["startLifetime"] = main.startLifetime.constant,
                        ["startSpeed"] = main.startSpeed.constant,
                        ["startSize"] = main.startSize.constant,
                        ["startColor"] = new float[] { main.startColor.color.r, main.startColor.color.g, main.startColor.color.b, main.startColor.color.a }
                    },
                    ["emission"] = new Dictionary<string, object>
                    {
                        ["enabled"] = emission.enabled,
                        ["rateOverTime"] = emission.rateOverTime.constant
                    },
                    ["shape"] = new Dictionary<string, object>
                    {
                        ["enabled"] = shape.enabled,
                        ["shapeType"] = shape.shapeType.ToString(),
                        ["radius"] = shape.radius
                    }
                };

                return JsonHelper.CreateSuccessResponse("Particle system properties retrieved successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"GetParticleSystemProperties error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error getting particle system properties: {ex.Message}");
            }
        }

        private static string SetupVfxGraph(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();
                string vfxAssetPath = parameters["vfx_asset_path"]?.ToString();

                switch (action?.ToLower())
                {
                    case "create":
                        return CreateVfxGraph(parameters);
                    case "modify":
                        return ModifyVfxGraph(parameters);
                    case "play":
                        return PlayVfxGraph(gameObjectName);
                    case "stop":
                        return StopVfxGraph(gameObjectName);
                    case "pause":
                        return PauseVfxGraph(gameObjectName);
                    case "get_properties":
                        return GetVfxGraphProperties(gameObjectName);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown VFX Graph action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"SetupVfxGraph error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error with VFX Graph: {ex.Message}");
            }
        }

        private static string CreateVfxGraph(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString() ?? "VFXGraph";
                string vfxAssetPath = parameters["vfx_asset_path"]?.ToString();

                if (string.IsNullOrEmpty(vfxAssetPath))
                {
                    return JsonHelper.CreateErrorResponse("VFX asset path is required");
                }

                VisualEffectAsset vfxAsset = AssetDatabase.LoadAssetAtPath<VisualEffectAsset>(vfxAssetPath);
                if (vfxAsset == null)
                {
                    return JsonHelper.CreateErrorResponse($"VFX asset not found at path: {vfxAssetPath}");
                }

                GameObject go = new GameObject(gameObjectName);
                VisualEffect vfx = go.AddComponent<VisualEffect>();
                vfx.visualEffectAsset = vfxAsset;

                // Configure VFX properties
                if (parameters["properties"] != null)
                {
                    var properties = parameters["properties"].ToObject<Dictionary<string, object>>();
                    foreach (var prop in properties)
                    {
                        SetVfxProperty(vfx, prop.Key, prop.Value);
                    }
                }

                if (parameters["spawn_rate"] != null)
                {
                    float spawnRate = parameters["spawn_rate"].Value<float>();
                    if (vfx.HasFloat("SpawnRate"))
                    {
                        vfx.SetFloat("SpawnRate", spawnRate);
                    }
                }

                if (parameters["capacity"] != null)
                {
                    int capacity = parameters["capacity"].Value<int>();
                    if (vfx.HasInt("Capacity"))
                    {
                        vfx.SetInt("Capacity", capacity);
                    }
                }

                if (parameters["bounds"] != null)
                {
                    var boundsArray = parameters["bounds"].ToObject<float[]>();
                    if (boundsArray.Length >= 6)
                    {
                        Vector3 center = new Vector3(boundsArray[0], boundsArray[1], boundsArray[2]);
                        Vector3 size = new Vector3(boundsArray[3], boundsArray[4], boundsArray[5]);
                        // Fixed: cullingFlags not available in Unity 6.2 VisualEffect API - removing this line
                    // vfx.cullingFlags = VFXCullingFlags.CullBounds;
                        // Note: Bounds setting might require custom implementation based on VFX Graph setup
                    }
                }

                if (parameters["random_seed"] != null)
                {
                    uint randomSeed = parameters["random_seed"].Value<uint>();
                    // Fixed: randomSeed property is now startSeed in Unity 6.2
                    vfx.startSeed = randomSeed;
                }

                bool playOnAwake = parameters["play_on_awake"]?.Value<bool>() ?? true;
                if (playOnAwake)
                {
                    vfx.Play();
                }

                if (parameters["initial_event"] != null)
                {
                    string initialEvent = parameters["initial_event"].ToString();
                    vfx.SendEvent(initialEvent);
                }

                Selection.activeGameObject = go;

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = go.name,
                    ["instanceId"] = go.GetInstanceID(),
                    ["isPlaying"] = vfx.isActiveAndEnabled,
                    ["aliveParticleCount"] = vfx.aliveParticleCount
                };

                return JsonHelper.CreateSuccessResponse("VFX Graph created successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateVfxGraph error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating VFX Graph: {ex.Message}");
            }
        }

        private static void SetVfxProperty(VisualEffect vfx, string propertyName, object value)
        {
            try
            {
                if (vfx.HasFloat(propertyName) && (value is float || value is double || value is int))
                {
                    vfx.SetFloat(propertyName, Convert.ToSingle(value));
                }
                else if (vfx.HasInt(propertyName) && value is int)
                {
                    vfx.SetInt(propertyName, (int)value);
                }
                else if (vfx.HasBool(propertyName) && value is bool)
                {
                    vfx.SetBool(propertyName, (bool)value);
                }
                else if (vfx.HasVector3(propertyName) && value is JArray)
                {
                    var vectorArray = ((JArray)value).ToObject<float[]>();
                    if (vectorArray.Length >= 3)
                    {
                        vfx.SetVector3(propertyName, new Vector3(vectorArray[0], vectorArray[1], vectorArray[2]));
                    }
                }
                else if (vfx.HasVector4(propertyName) && value is JArray)
                {
                    var vectorArray = ((JArray)value).ToObject<float[]>();
                    if (vectorArray.Length >= 4)
                    {
                        vfx.SetVector4(propertyName, new Vector4(vectorArray[0], vectorArray[1], vectorArray[2], vectorArray[3]));
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to set VFX property '{propertyName}': {ex.Message}");
            }
        }

        private static string ModifyVfxGraph(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for modification");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                VisualEffect vfx = go.GetComponent<VisualEffect>();
                if (vfx == null)
                {
                    return JsonHelper.CreateErrorResponse($"VisualEffect component not found on '{gameObjectName}'");
                }

                // Apply modifications
                if (parameters["properties"] != null)
                {
                    var properties = parameters["properties"].ToObject<Dictionary<string, object>>();
                    foreach (var prop in properties)
                    {
                        SetVfxProperty(vfx, prop.Key, prop.Value);
                    }
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = go.name,
                    ["aliveParticleCount"] = vfx.aliveParticleCount
                };

                return JsonHelper.CreateSuccessResponse("VFX Graph modified successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"ModifyVfxGraph error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error modifying VFX Graph: {ex.Message}");
            }
        }

        private static string PlayVfxGraph(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                VisualEffect vfx = go.GetComponent<VisualEffect>();
                if (vfx == null)
                {
                    return JsonHelper.CreateErrorResponse($"VisualEffect component not found on '{gameObjectName}'");
                }

                vfx.Play();
                
                return JsonHelper.CreateSuccessResponse($"VFX Graph '{gameObjectName}' started playing");
            }
            catch (Exception ex)
            {
                Debug.LogError($"PlayVfxGraph error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error playing VFX Graph: {ex.Message}");
            }
        }

        private static string StopVfxGraph(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                VisualEffect vfx = go.GetComponent<VisualEffect>();
                if (vfx == null)
                {
                    return JsonHelper.CreateErrorResponse($"VisualEffect component not found on '{gameObjectName}'");
                }

                vfx.Stop();
                
                return JsonHelper.CreateSuccessResponse($"VFX Graph '{gameObjectName}' stopped");
            }
            catch (Exception ex)
            {
                Debug.LogError($"StopVfxGraph error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error stopping VFX Graph: {ex.Message}");
            }
        }

        private static string PauseVfxGraph(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                VisualEffect vfx = go.GetComponent<VisualEffect>();
                if (vfx == null)
                {
                    return JsonHelper.CreateErrorResponse($"VisualEffect component not found on '{gameObjectName}'");
                }

                vfx.pause = !vfx.pause;
                string status = vfx.pause ? "paused" : "resumed";
                
                return JsonHelper.CreateSuccessResponse($"VFX Graph '{gameObjectName}' {status}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"PauseVfxGraph error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error pausing VFX Graph: {ex.Message}");
            }
        }

        private static string GetVfxGraphProperties(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                {
                    return JsonHelper.CreateErrorResponse($"GameObject '{gameObjectName}' not found");
                }

                VisualEffect vfx = go.GetComponent<VisualEffect>();
                if (vfx == null)
                {
                    return JsonHelper.CreateErrorResponse($"VisualEffect component not found on '{gameObjectName}'");
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = go.name,
                    ["isActiveAndEnabled"] = vfx.isActiveAndEnabled,
                    ["pause"] = vfx.pause,
                    ["aliveParticleCount"] = vfx.aliveParticleCount,
                    ["randomSeed"] = vfx.startSeed, // Fixed: randomSeed -> startSeed
                    // Fixed: cullingFlags not available in Unity 6.2 VisualEffect API
                    ["culled"] = vfx.culled // Using culled property instead
                };

                return JsonHelper.CreateSuccessResponse("VFX Graph properties retrieved successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"GetVfxGraphProperties error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error getting VFX Graph properties: {ex.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Implementações especializadas para diferentes tipos de efeitos VFX.
        /// Cada tipo usa configurações otimizadas para Unity 6.2 Visual Effect Graph.
        /// </summary>
        
        private static string CreateFireEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();

                switch (action?.ToLower())
                {
                    case "create":
                        return CreateFireEffect(parameters);
                    case "modify":
                        return ModifyFireEffect(parameters);
                    case "delete":
                        return DeleteFireEffect(gameObjectName);
                    case "get_properties":
                        return GetFireEffectProperties(gameObjectName);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown fire effect action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateFireEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating fire effects: {ex.Message}");
            }
        }

        private static string CreateFireEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString() ?? "FireEffect";
                string fireType = parameters["fire_type"]?.ToString() ?? "campfire";
                float intensity = parameters["intensity"]?.Value<float>() ?? 1.0f;
                float flameHeight = parameters["flame_height"]?.Value<float>() ?? 2.0f;
                float flameWidth = parameters["flame_width"]?.Value<float>() ?? 1.0f;
                float temperature = parameters["temperature"]?.Value<float>() ?? 1000.0f;
                float smokeDensity = parameters["smoke_density"]?.Value<float>() ?? 0.5f;
                int sparkCount = parameters["spark_count"]?.Value<int>() ?? 50;
                float windInfluence = parameters["wind_influence"]?.Value<float>() ?? 0.1f;

                GameObject fireGO = new GameObject(gameObjectName);
                
                // Create main flame particle system
                GameObject flameGO = new GameObject("Flames");
                flameGO.transform.SetParent(fireGO.transform);
                ParticleSystem flamePS = flameGO.AddComponent<ParticleSystem>();
                
                var flameMain = flamePS.main;
                var flameEmission = flamePS.emission;
                var flameShape = flamePS.shape;
                var flameVelocity = flamePS.velocityOverLifetime;
                var flameSizeOverLifetime = flamePS.sizeOverLifetime;
                var flameColorOverLifetime = flamePS.colorOverLifetime;
                var flameRenderer = flamePS.GetComponent<ParticleSystemRenderer>();

                // Configure flame main module
                flameMain.startLifetime = 1.0f + (intensity * 0.5f);
                flameMain.startSpeed = 2.0f + (intensity * 1.0f);
                flameMain.startSize = 0.5f * flameWidth;
                flameMain.startColor = new Color(1.0f, 0.4f, 0.1f, 0.8f);
                flameMain.maxParticles = Mathf.RoundToInt(100 * intensity);
                flameMain.simulationSpace = ParticleSystemSimulationSpace.Local;

                // Configure flame emission
                flameEmission.rateOverTime = 50 * intensity;
                
                // Configure flame shape
                flameShape.enabled = true;
                flameShape.shapeType = ParticleSystemShapeType.Circle;
                flameShape.radius = flameWidth * 0.5f;
                
                // Configure flame velocity over lifetime
                flameVelocity.enabled = true;
                flameVelocity.space = ParticleSystemSimulationSpace.Local;
                flameVelocity.y = new ParticleSystem.MinMaxCurve(flameHeight * 0.5f, flameHeight);
                flameVelocity.x = new ParticleSystem.MinMaxCurve(-windInfluence, windInfluence);
                
                // Configure flame size over lifetime
                flameSizeOverLifetime.enabled = true;
                AnimationCurve flameSizeCurve = new AnimationCurve();
                flameSizeCurve.AddKey(0f, 0.2f);
                flameSizeCurve.AddKey(0.3f, 1.0f);
                flameSizeCurve.AddKey(1f, 0.1f);
                flameSizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, flameSizeCurve);
                
                // Configure flame color over lifetime
                flameColorOverLifetime.enabled = true;
                Gradient flameGradient = new Gradient();
                GradientColorKey[] flameColorKeys = new GradientColorKey[4];
                flameColorKeys[0] = new GradientColorKey(new Color(1.0f, 0.2f, 0.0f), 0.0f); // Red base
                flameColorKeys[1] = new GradientColorKey(new Color(1.0f, 0.6f, 0.0f), 0.3f); // Orange
                flameColorKeys[2] = new GradientColorKey(new Color(1.0f, 1.0f, 0.2f), 0.7f); // Yellow
                flameColorKeys[3] = new GradientColorKey(new Color(0.8f, 0.8f, 0.8f), 1.0f); // White hot
                
                GradientAlphaKey[] flameAlphaKeys = new GradientAlphaKey[3];
                flameAlphaKeys[0] = new GradientAlphaKey(0.8f, 0.0f);
                flameAlphaKeys[1] = new GradientAlphaKey(1.0f, 0.5f);
                flameAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
                
                flameGradient.SetKeys(flameColorKeys, flameAlphaKeys);
                flameColorOverLifetime.color = flameGradient;
                
                // Configure flame renderer
                flameRenderer.material = CreateFireMaterial("FlameMaterial");
                flameRenderer.renderMode = ParticleSystemRenderMode.Billboard;
                
                // Create smoke particle system
                GameObject smokeGO = new GameObject("Smoke");
                smokeGO.transform.SetParent(fireGO.transform);
                smokeGO.transform.localPosition = new Vector3(0, flameHeight * 0.7f, 0);
                ParticleSystem smokePS = smokeGO.AddComponent<ParticleSystem>();
                
                var smokeMain = smokePS.main;
                var smokeEmission = smokePS.emission;
                var smokeShape = smokePS.shape;
                var smokeVelocity = smokePS.velocityOverLifetime;
                var smokeSizeOverLifetime = smokePS.sizeOverLifetime;
                var smokeColorOverLifetime = smokePS.colorOverLifetime;
                var smokeRenderer = smokePS.GetComponent<ParticleSystemRenderer>();
                
                // Configure smoke
                smokeMain.startLifetime = 3.0f + (smokeDensity * 2.0f);
                smokeMain.startSpeed = 1.0f;
                smokeMain.startSize = 1.0f;
                smokeMain.startColor = new Color(0.2f, 0.2f, 0.2f, 0.3f * smokeDensity);
                smokeMain.maxParticles = Mathf.RoundToInt(50 * smokeDensity);
                
                smokeEmission.rateOverTime = 20 * smokeDensity;
                
                smokeShape.enabled = true;
                smokeShape.shapeType = ParticleSystemShapeType.Circle;
                smokeShape.radius = flameWidth * 0.3f;
                
                smokeVelocity.enabled = true;
                smokeVelocity.space = ParticleSystemSimulationSpace.Local;
                smokeVelocity.y = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
                smokeVelocity.x = new ParticleSystem.MinMaxCurve(-windInfluence * 2, windInfluence * 2);
                
                smokeSizeOverLifetime.enabled = true;
                AnimationCurve smokeSizeCurve = new AnimationCurve();
                smokeSizeCurve.AddKey(0f, 0.5f);
                smokeSizeCurve.AddKey(0.5f, 1.0f);
                smokeSizeCurve.AddKey(1f, 2.0f);
                smokeSizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, smokeSizeCurve);
                
                smokeColorOverLifetime.enabled = true;
                Gradient smokeGradient = new Gradient();
                GradientColorKey[] smokeColorKeys = new GradientColorKey[3];
                smokeColorKeys[0] = new GradientColorKey(new Color(0.1f, 0.1f, 0.1f), 0.0f);
                smokeColorKeys[1] = new GradientColorKey(new Color(0.3f, 0.3f, 0.3f), 0.5f);
                smokeColorKeys[2] = new GradientColorKey(new Color(0.6f, 0.6f, 0.6f), 1.0f);
                
                GradientAlphaKey[] smokeAlphaKeys = new GradientAlphaKey[3];
                smokeAlphaKeys[0] = new GradientAlphaKey(0.5f * smokeDensity, 0.0f);
                smokeAlphaKeys[1] = new GradientAlphaKey(0.8f * smokeDensity, 0.3f);
                smokeAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
                
                smokeGradient.SetKeys(smokeColorKeys, smokeAlphaKeys);
                smokeColorOverLifetime.color = smokeGradient;
                
                smokeRenderer.material = CreateSmokeMaterial("SmokeMaterial");
                smokeRenderer.renderMode = ParticleSystemRenderMode.Billboard;
                
                // Create sparks particle system
                if (sparkCount > 0)
                {
                    GameObject sparksGO = new GameObject("Sparks");
                    sparksGO.transform.SetParent(fireGO.transform);
                    ParticleSystem sparksPS = sparksGO.AddComponent<ParticleSystem>();
                    
                    var sparksMain = sparksPS.main;
                    var sparksEmission = sparksPS.emission;
                    var sparksShape = sparksPS.shape;
                    var sparksVelocity = sparksPS.velocityOverLifetime;
                    var sparksGravity = sparksPS.forceOverLifetime;
                    var sparksRenderer = sparksPS.GetComponent<ParticleSystemRenderer>();
                    
                    sparksMain.startLifetime = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
                    sparksMain.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 8.0f);
                    sparksMain.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.05f);
                    sparksMain.startColor = new Color(1.0f, 0.8f, 0.2f, 1.0f);
                    sparksMain.maxParticles = sparkCount;
                    
                    sparksEmission.rateOverTime = sparkCount * 0.2f;
                    
                    sparksShape.enabled = true;
                    sparksShape.shapeType = ParticleSystemShapeType.Circle;
                    sparksShape.radius = flameWidth * 0.2f;
                    
                    sparksVelocity.enabled = true;
                    sparksVelocity.space = ParticleSystemSimulationSpace.Local;
                    sparksVelocity.radial = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
                    
                    sparksGravity.enabled = true;
                    sparksGravity.space = ParticleSystemSimulationSpace.World;
                    sparksGravity.y = new ParticleSystem.MinMaxCurve(-9.81f);
                    
                    sparksRenderer.material = CreateSparkMaterial("SparkMaterial");
                    sparksRenderer.renderMode = ParticleSystemRenderMode.Billboard;
                }
                
                // Add light component for fire glow
                Light fireLight = fireGO.AddComponent<Light>();
                fireLight.type = LightType.Point;
                fireLight.color = new Color(1.0f, 0.6f, 0.2f);
                fireLight.intensity = intensity * 2.0f;
                fireLight.range = flameHeight * 2.0f;
                fireLight.shadows = LightShadows.Soft;
                
                // Add audio source for fire crackling
                AudioSource fireAudio = fireGO.AddComponent<AudioSource>();
                fireAudio.loop = true;
                fireAudio.volume = intensity * 0.5f;
                fireAudio.pitch = 1.0f + (UnityEngine.Random.Range(-0.1f, 0.1f));
                
                Selection.activeGameObject = fireGO;
                
                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = fireGO.name,
                    ["instanceId"] = fireGO.GetInstanceID(),
                    ["fireType"] = fireType,
                    ["intensity"] = intensity,
                    ["flameHeight"] = flameHeight,
                    ["flameWidth"] = flameWidth,
                    ["temperature"] = temperature,
                    ["smokeDensity"] = smokeDensity,
                    ["sparkCount"] = sparkCount,
                    ["windInfluence"] = windInfluence,
                    ["components"] = new string[] { "Flames", "Smoke", "Sparks", "Light", "AudioSource" }
                };

                return JsonHelper.CreateSuccessResponse("Fire effect created successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateFireEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating fire effect: {ex.Message}");
            }
        }
        
        private static Material CreateFireMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for additive blending
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateSmokeMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for alpha blending
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateSparkMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for additive blending
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static string ModifyFireEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for modification");
                }

                GameObject fireGO = GameObject.Find(gameObjectName);
                if (fireGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Fire effect GameObject '{gameObjectName}' not found");
                }

                // Modify intensity if provided
                if (parameters["intensity"] != null)
                {
                    float newIntensity = parameters["intensity"].Value<float>();
                    
                    // Update flame particle system
                    Transform flameTransform = fireGO.transform.Find("Flames");
                    if (flameTransform != null)
                    {
                        ParticleSystem flamePS = flameTransform.GetComponent<ParticleSystem>();
                        if (flamePS != null)
                        {
                            var main = flamePS.main;
                            var emission = flamePS.emission;
                            main.maxParticles = Mathf.RoundToInt(100 * newIntensity);
                            emission.rateOverTime = 50 * newIntensity;
                        }
                    }
                    
                    // Update light intensity
                    Light fireLight = fireGO.GetComponent<Light>();
                    if (fireLight != null)
                    {
                        fireLight.intensity = newIntensity * 2.0f;
                    }
                    
                    // Update audio volume
                    AudioSource fireAudio = fireGO.GetComponent<AudioSource>();
                    if (fireAudio != null)
                    {
                        fireAudio.volume = newIntensity * 0.5f;
                    }
                }

                return JsonHelper.CreateSuccessResponse("Fire effect modified successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ModifyFireEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error modifying fire effect: {ex.Message}");
            }
        }
        
        private static string DeleteFireEffect(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for deletion");
                }

                GameObject fireGO = GameObject.Find(gameObjectName);
                if (fireGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Fire effect GameObject '{gameObjectName}' not found");
                }

                UnityEngine.Object.DestroyImmediate(fireGO);
                
                return JsonHelper.CreateSuccessResponse($"Fire effect '{gameObjectName}' deleted successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"DeleteFireEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error deleting fire effect: {ex.Message}");
            }
        }
        
        private static string GetFireEffectProperties(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject fireGO = GameObject.Find(gameObjectName);
                if (fireGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Fire effect GameObject '{gameObjectName}' not found");
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = fireGO.name,
                    ["position"] = new float[] { fireGO.transform.position.x, fireGO.transform.position.y, fireGO.transform.position.z },
                    ["rotation"] = new float[] { fireGO.transform.rotation.x, fireGO.transform.rotation.y, fireGO.transform.rotation.z, fireGO.transform.rotation.w },
                    ["scale"] = new float[] { fireGO.transform.localScale.x, fireGO.transform.localScale.y, fireGO.transform.localScale.z }
                };
                
                // Get flame properties
                Transform flameTransform = fireGO.transform.Find("Flames");
                if (flameTransform != null)
                {
                    ParticleSystem flamePS = flameTransform.GetComponent<ParticleSystem>();
                    if (flamePS != null)
                    {
                        result["flames"] = new Dictionary<string, object>
                        {
                            ["isPlaying"] = flamePS.isPlaying,
                            ["particleCount"] = flamePS.particleCount,
                            ["maxParticles"] = flamePS.main.maxParticles,
                            ["emissionRate"] = flamePS.emission.rateOverTime.constant
                        };
                    }
                }
                
                // Get light properties
                Light fireLight = fireGO.GetComponent<Light>();
                if (fireLight != null)
                {
                    result["light"] = new Dictionary<string, object>
                    {
                        ["enabled"] = fireLight.enabled,
                        ["intensity"] = fireLight.intensity,
                        ["range"] = fireLight.range,
                        ["color"] = new float[] { fireLight.color.r, fireLight.color.g, fireLight.color.b, fireLight.color.a }
                    };
                }
                
                // Get audio properties
                AudioSource fireAudio = fireGO.GetComponent<AudioSource>();
                if (fireAudio != null)
                {
                    result["audio"] = new Dictionary<string, object>
                    {
                        ["isPlaying"] = fireAudio.isPlaying,
                        ["volume"] = fireAudio.volume,
                        ["pitch"] = fireAudio.pitch,
                        ["loop"] = fireAudio.loop
                    };
                }

                return JsonHelper.CreateSuccessResponse("Fire effect properties retrieved successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"GetFireEffectProperties error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error getting fire effect properties: {ex.Message}");
            }
        }

        private static string SetupWaterEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();

                switch (action?.ToLower())
                {
                    case "create":
                        return CreateWaterEffect(parameters);
                    case "modify":
                        return ModifyWaterEffect(parameters);
                    case "delete":
                        return DeleteWaterEffect(gameObjectName);
                    case "get_properties":
                        return GetWaterEffectProperties(gameObjectName);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown water effect action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"SetupWaterEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error setting up water effects: {ex.Message}");
            }
        }
        
        private static string CreateWaterEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString() ?? "WaterEffect";
                string waterType = parameters["water_type"]?.ToString() ?? "splash";
                float flowRate = parameters["flow_rate"]?.Value<float>() ?? 1.0f;
                float splashIntensity = parameters["splash_intensity"]?.Value<float>() ?? 1.0f;
                float foamAmount = parameters["foam_amount"]?.Value<float>() ?? 0.5f;
                float viscosity = parameters["viscosity"]?.Value<float>() ?? 1.0f;
                float transparency = parameters["transparency"]?.Value<float>() ?? 0.8f;
                
                GameObject waterGO = new GameObject(gameObjectName);
                
                switch (waterType.ToLower())
                {
                    case "splash":
                        CreateSplashEffect(waterGO, splashIntensity, foamAmount);
                        break;
                    case "wave":
                        CreateWaveEffect(waterGO, flowRate, foamAmount);
                        break;
                    case "underwater":
                        CreateUnderwaterEffect(waterGO, viscosity, transparency);
                        break;
                    case "waterfall":
                        CreateWaterfallEffect(waterGO, flowRate, splashIntensity);
                        break;
                    case "rain":
                        CreateRainEffect(waterGO, flowRate, splashIntensity);
                        break;
                    default:
                        CreateSplashEffect(waterGO, splashIntensity, foamAmount);
                        break;
                }
                
                Selection.activeGameObject = waterGO;
                
                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = waterGO.name,
                    ["instanceId"] = waterGO.GetInstanceID(),
                    ["waterType"] = waterType,
                    ["flowRate"] = flowRate,
                    ["splashIntensity"] = splashIntensity,
                    ["foamAmount"] = foamAmount,
                    ["viscosity"] = viscosity,
                    ["transparency"] = transparency
                };

                return JsonHelper.CreateSuccessResponse("Water effect created successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateWaterEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating water effect: {ex.Message}");
            }
        }
        
        private static void CreateSplashEffect(GameObject parent, float intensity, float foamAmount)
        {
            // Main splash particles
            GameObject splashGO = new GameObject("Splash");
            splashGO.transform.SetParent(parent.transform);
            ParticleSystem splashPS = splashGO.AddComponent<ParticleSystem>();
            
            var splashMain = splashPS.main;
            var splashEmission = splashPS.emission;
            var splashShape = splashPS.shape;
            var splashVelocity = splashPS.velocityOverLifetime;
            var splashGravity = splashPS.forceOverLifetime;
            var splashSizeOverLifetime = splashPS.sizeOverLifetime;
            var splashColorOverLifetime = splashPS.colorOverLifetime;
            var splashRenderer = splashPS.GetComponent<ParticleSystemRenderer>();
            
            // Configure splash main module
            splashMain.startLifetime = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            splashMain.startSpeed = new ParticleSystem.MinMaxCurve(2.0f * intensity, 8.0f * intensity);
            splashMain.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            splashMain.startColor = new Color(0.2f, 0.6f, 1.0f, 0.8f);
            splashMain.maxParticles = Mathf.RoundToInt(200 * intensity);
            splashMain.simulationSpace = ParticleSystemSimulationSpace.World;
            
            // Configure splash emission
            splashEmission.rateOverTime = 0;
            splashEmission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, Mathf.RoundToInt(50 * intensity))
            });
            
            // Configure splash shape
            splashShape.enabled = true;
            splashShape.shapeType = ParticleSystemShapeType.Hemisphere;
            splashShape.radius = 0.5f;
            
            // Configure splash velocity
            splashVelocity.enabled = true;
            splashVelocity.space = ParticleSystemSimulationSpace.Local;
            splashVelocity.radial = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            
            // Configure gravity
            splashGravity.enabled = true;
            splashGravity.space = ParticleSystemSimulationSpace.World;
            splashGravity.y = new ParticleSystem.MinMaxCurve(-9.81f);
            
            // Configure size over lifetime
            splashSizeOverLifetime.enabled = true;
            AnimationCurve splashSizeCurve = new AnimationCurve();
            splashSizeCurve.AddKey(0f, 1.0f);
            splashSizeCurve.AddKey(0.5f, 0.8f);
            splashSizeCurve.AddKey(1f, 0.2f);
            splashSizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, splashSizeCurve);
            
            // Configure color over lifetime
            splashColorOverLifetime.enabled = true;
            Gradient splashGradient = new Gradient();
            GradientColorKey[] splashColorKeys = new GradientColorKey[2];
            splashColorKeys[0] = new GradientColorKey(new Color(0.2f, 0.6f, 1.0f), 0.0f);
            splashColorKeys[1] = new GradientColorKey(new Color(0.8f, 0.9f, 1.0f), 1.0f);
            
            GradientAlphaKey[] splashAlphaKeys = new GradientAlphaKey[3];
            splashAlphaKeys[0] = new GradientAlphaKey(0.8f, 0.0f);
            splashAlphaKeys[1] = new GradientAlphaKey(1.0f, 0.3f);
            splashAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            splashGradient.SetKeys(splashColorKeys, splashAlphaKeys);
            splashColorOverLifetime.color = splashGradient;
            
            splashRenderer.material = CreateWaterMaterial("SplashMaterial");
            splashRenderer.renderMode = ParticleSystemRenderMode.Billboard;
            
            // Create foam particles if foam amount > 0
            if (foamAmount > 0)
            {
                GameObject foamGO = new GameObject("Foam");
                foamGO.transform.SetParent(parent.transform);
                ParticleSystem foamPS = foamGO.AddComponent<ParticleSystem>();
                
                var foamMain = foamPS.main;
                var foamEmission = foamPS.emission;
                var foamShape = foamPS.shape;
                var foamRenderer = foamPS.GetComponent<ParticleSystemRenderer>();
                
                foamMain.startLifetime = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
                foamMain.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
                foamMain.startSize = new ParticleSystem.MinMaxCurve(0.2f, 0.5f);
                foamMain.startColor = new Color(1.0f, 1.0f, 1.0f, 0.6f * foamAmount);
                foamMain.maxParticles = Mathf.RoundToInt(100 * foamAmount);
                
                foamEmission.rateOverTime = 30 * foamAmount;
                
                foamShape.enabled = true;
                foamShape.shapeType = ParticleSystemShapeType.Circle;
                foamShape.radius = 1.0f;
                
                foamRenderer.material = CreateFoamMaterial("FoamMaterial");
                foamRenderer.renderMode = ParticleSystemRenderMode.Billboard;
            }
        }
        
        private static void CreateWaveEffect(GameObject parent, float flowRate, float foamAmount)
        {
            // Wave ripples
            GameObject waveGO = new GameObject("Waves");
            waveGO.transform.SetParent(parent.transform);
            ParticleSystem wavePS = waveGO.AddComponent<ParticleSystem>();
            
            var waveMain = wavePS.main;
            var waveEmission = wavePS.emission;
            var waveShape = wavePS.shape;
            var waveSizeOverLifetime = wavePS.sizeOverLifetime;
            var waveColorOverLifetime = wavePS.colorOverLifetime;
            var waveRenderer = wavePS.GetComponent<ParticleSystemRenderer>();
            
            waveMain.startLifetime = 2.0f + (flowRate * 1.0f);
            waveMain.startSpeed = 0.1f;
            waveMain.startSize = 0.5f;
            waveMain.startColor = new Color(0.3f, 0.7f, 1.0f, 0.4f);
            waveMain.maxParticles = Mathf.RoundToInt(50 * flowRate);
            waveMain.simulationSpace = ParticleSystemSimulationSpace.Local;
            
            waveEmission.rateOverTime = 10 * flowRate;
            
            waveShape.enabled = true;
            waveShape.shapeType = ParticleSystemShapeType.Circle;
            waveShape.radius = 0.1f;
            
            // Expanding wave effect
            waveSizeOverLifetime.enabled = true;
            AnimationCurve waveSizeCurve = new AnimationCurve();
            waveSizeCurve.AddKey(0f, 0.1f);
            waveSizeCurve.AddKey(1f, 3.0f);
            waveSizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, waveSizeCurve);
            
            waveColorOverLifetime.enabled = true;
            Gradient waveGradient = new Gradient();
            GradientAlphaKey[] waveAlphaKeys = new GradientAlphaKey[3];
            waveAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            waveAlphaKeys[1] = new GradientAlphaKey(0.6f, 0.3f);
            waveAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            GradientColorKey[] waveColorKeys = new GradientColorKey[1];
            waveColorKeys[0] = new GradientColorKey(new Color(0.3f, 0.7f, 1.0f), 0.0f);
            
            waveGradient.SetKeys(waveColorKeys, waveAlphaKeys);
            waveColorOverLifetime.color = waveGradient;
            
            waveRenderer.material = CreateWaterMaterial("WaveMaterial");
            waveRenderer.renderMode = ParticleSystemRenderMode.HorizontalBillboard;
        }
        
        private static void CreateUnderwaterEffect(GameObject parent, float viscosity, float transparency)
        {
            // Underwater bubbles
            GameObject bubblesGO = new GameObject("Bubbles");
            bubblesGO.transform.SetParent(parent.transform);
            ParticleSystem bubblesPS = bubblesGO.AddComponent<ParticleSystem>();
            
            var bubblesMain = bubblesPS.main;
            var bubblesEmission = bubblesPS.emission;
            var bubblesShape = bubblesPS.shape;
            var bubblesVelocity = bubblesPS.velocityOverLifetime;
            var bubblesSizeOverLifetime = bubblesPS.sizeOverLifetime;
            var bubblesRenderer = bubblesPS.GetComponent<ParticleSystemRenderer>();
            
            bubblesMain.startLifetime = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            bubblesMain.startSpeed = new ParticleSystem.MinMaxCurve(0.5f / viscosity, 2.0f / viscosity);
            bubblesMain.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.2f);
            bubblesMain.startColor = new Color(0.8f, 0.9f, 1.0f, transparency);
            bubblesMain.maxParticles = 100;
            
            bubblesEmission.rateOverTime = 20;
            
            bubblesShape.enabled = true;
            bubblesShape.shapeType = ParticleSystemShapeType.Box;
            bubblesShape.scale = new Vector3(2.0f, 0.1f, 2.0f);
            
            bubblesVelocity.enabled = true;
            bubblesVelocity.space = ParticleSystemSimulationSpace.Local;
            bubblesVelocity.y = new ParticleSystem.MinMaxCurve(1.0f / viscosity, 3.0f / viscosity);
            
            bubblesSizeOverLifetime.enabled = true;
            AnimationCurve bubbleSizeCurve = new AnimationCurve();
            bubbleSizeCurve.AddKey(0f, 0.5f);
            bubbleSizeCurve.AddKey(0.8f, 1.0f);
            bubbleSizeCurve.AddKey(1f, 1.2f);
            bubblesSizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, bubbleSizeCurve);
            
            bubblesRenderer.material = CreateBubbleMaterial("BubbleMaterial");
            bubblesRenderer.renderMode = ParticleSystemRenderMode.Billboard;
        }
        
        private static void CreateWaterfallEffect(GameObject parent, float flowRate, float splashIntensity)
        {
            // Waterfall stream
            GameObject streamGO = new GameObject("WaterfallStream");
            streamGO.transform.SetParent(parent.transform);
            ParticleSystem streamPS = streamGO.AddComponent<ParticleSystem>();
            
            var streamMain = streamPS.main;
            var streamEmission = streamPS.emission;
            var streamShape = streamPS.shape;
            var streamVelocity = streamPS.velocityOverLifetime;
            var streamGravity = streamPS.forceOverLifetime;
            var streamRenderer = streamPS.GetComponent<ParticleSystemRenderer>();
            
            streamMain.startLifetime = 3.0f;
            streamMain.startSpeed = 5.0f * flowRate;
            streamMain.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            streamMain.startColor = new Color(0.2f, 0.6f, 1.0f, 0.8f);
            streamMain.maxParticles = Mathf.RoundToInt(500 * flowRate);
            
            streamEmission.rateOverTime = 200 * flowRate;
            
            streamShape.enabled = true;
            streamShape.shapeType = ParticleSystemShapeType.Rectangle;
            streamShape.scale = new Vector3(1.0f, 0.1f, 0.1f);
            
            streamVelocity.enabled = true;
            streamVelocity.space = ParticleSystemSimulationSpace.Local;
            streamVelocity.y = new ParticleSystem.MinMaxCurve(-10.0f * flowRate);
            
            streamGravity.enabled = true;
            streamGravity.space = ParticleSystemSimulationSpace.World;
            streamGravity.y = new ParticleSystem.MinMaxCurve(-9.81f);
            
            streamRenderer.material = CreateWaterMaterial("WaterfallMaterial");
            streamRenderer.renderMode = ParticleSystemRenderMode.Stretch;
            streamRenderer.lengthScale = 2.0f;
            
            // Add splash at bottom
            GameObject splashGO = new GameObject("WaterfallSplash");
            splashGO.transform.SetParent(parent.transform);
            splashGO.transform.localPosition = new Vector3(0, -5.0f, 0);
            CreateSplashEffect(splashGO, splashIntensity, 0.8f);
        }
        
        private static void CreateRainEffect(GameObject parent, float flowRate, float intensity)
        {
            // Rain drops
            GameObject rainGO = new GameObject("Rain");
            rainGO.transform.SetParent(parent.transform);
            ParticleSystem rainPS = rainGO.AddComponent<ParticleSystem>();
            
            var rainMain = rainPS.main;
            var rainEmission = rainPS.emission;
            var rainShape = rainPS.shape;
            var rainVelocity = rainPS.velocityOverLifetime;
            var rainRenderer = rainPS.GetComponent<ParticleSystemRenderer>();
            
            rainMain.startLifetime = 2.0f;
            rainMain.startSpeed = 10.0f * intensity;
            rainMain.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.05f);
            rainMain.startColor = new Color(0.7f, 0.8f, 1.0f, 0.8f);
            rainMain.maxParticles = Mathf.RoundToInt(1000 * flowRate);
            
            rainEmission.rateOverTime = 500 * flowRate;
            
            rainShape.enabled = true;
            rainShape.shapeType = ParticleSystemShapeType.Box;
            rainShape.scale = new Vector3(10.0f, 0.1f, 10.0f);
            
            rainVelocity.enabled = true;
            rainVelocity.space = ParticleSystemSimulationSpace.Local;
            rainVelocity.y = new ParticleSystem.MinMaxCurve(-15.0f * intensity);
            
            rainRenderer.material = CreateWaterMaterial("RainMaterial");
            rainRenderer.renderMode = ParticleSystemRenderMode.Stretch;
            rainRenderer.lengthScale = 0.5f;
        }
        
        private static Material CreateWaterMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for alpha blending
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateFoamMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for additive blending for foam
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateBubbleMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for alpha blending with refraction-like effect
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static string ModifyWaterEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for modification");
                }

                GameObject waterGO = GameObject.Find(gameObjectName);
                if (waterGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Water effect GameObject '{gameObjectName}' not found");
                }

                // Modify flow rate if provided
                if (parameters["flow_rate"] != null)
                {
                    float newFlowRate = parameters["flow_rate"].Value<float>();
                    
                    // Update all particle systems emission rates
                    ParticleSystem[] particleSystems = waterGO.GetComponentsInChildren<ParticleSystem>();
                    foreach (ParticleSystem ps in particleSystems)
                    {
                        var emission = ps.emission;
                        emission.rateOverTime = emission.rateOverTime.constant * newFlowRate;
                    }
                }

                return JsonHelper.CreateSuccessResponse("Water effect modified successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ModifyWaterEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error modifying water effect: {ex.Message}");
            }
        }
        
        private static string DeleteWaterEffect(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for deletion");
                }

                GameObject waterGO = GameObject.Find(gameObjectName);
                if (waterGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Water effect GameObject '{gameObjectName}' not found");
                }

                UnityEngine.Object.DestroyImmediate(waterGO);
                
                return JsonHelper.CreateSuccessResponse($"Water effect '{gameObjectName}' deleted successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"DeleteWaterEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error deleting water effect: {ex.Message}");
            }
        }
        
        private static string GetWaterEffectProperties(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject waterGO = GameObject.Find(gameObjectName);
                if (waterGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Water effect GameObject '{gameObjectName}' not found");
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = waterGO.name,
                    ["position"] = new float[] { waterGO.transform.position.x, waterGO.transform.position.y, waterGO.transform.position.z },
                    ["rotation"] = new float[] { waterGO.transform.rotation.x, waterGO.transform.rotation.y, waterGO.transform.rotation.z, waterGO.transform.rotation.w },
                    ["scale"] = new float[] { waterGO.transform.localScale.x, waterGO.transform.localScale.y, waterGO.transform.localScale.z }
                };
                
                // Get particle systems info
                ParticleSystem[] particleSystems = waterGO.GetComponentsInChildren<ParticleSystem>();
                var particleSystemsInfo = new List<Dictionary<string, object>>();
                
                foreach (ParticleSystem ps in particleSystems)
                {
                    particleSystemsInfo.Add(new Dictionary<string, object>
                    {
                        ["name"] = ps.gameObject.name,
                        ["isPlaying"] = ps.isPlaying,
                        ["particleCount"] = ps.particleCount,
                        ["maxParticles"] = ps.main.maxParticles,
                        ["emissionRate"] = ps.emission.rateOverTime.constant
                    });
                }
                
                result["particleSystems"] = particleSystemsInfo;

                return JsonHelper.CreateSuccessResponse("Water effect properties retrieved successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"GetWaterEffectProperties error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error getting water effect properties: {ex.Message}");
            }
        }

        private static string CreateExplosionEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();

                switch (action?.ToLower())
                {
                    case "create":
                        return CreateExplosionEffect(parameters);
                    case "modify":
                        return ModifyExplosionEffect(parameters);
                    case "delete":
                        return DeleteExplosionEffect(gameObjectName);
                    case "get_properties":
                        return GetExplosionEffectProperties(gameObjectName);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown explosion effect action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateExplosionEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating explosion effects: {ex.Message}");
            }
        }
        
        private static string CreateExplosionEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString() ?? "ExplosionEffect";
                string explosionType = parameters["explosion_type"]?.ToString() ?? "standard";
                float force = parameters["force"]?.Value<float>() ?? 10.0f;
                float radius = parameters["radius"]?.Value<float>() ?? 5.0f;
                float duration = parameters["duration"]?.Value<float>() ?? 2.0f;
                
                GameObject explosionGO = new GameObject(gameObjectName);
                
                // Create main explosion particle system
                GameObject mainExplosionGO = new GameObject("MainExplosion");
                mainExplosionGO.transform.SetParent(explosionGO.transform);
                ParticleSystem mainExplosionPS = mainExplosionGO.AddComponent<ParticleSystem>();
                
                ConfigureMainExplosion(mainExplosionPS, explosionType, force, radius, duration);
                
                // Create shockwave effect
                GameObject shockwaveGO = new GameObject("Shockwave");
                shockwaveGO.transform.SetParent(explosionGO.transform);
                ParticleSystem shockwavePS = shockwaveGO.AddComponent<ParticleSystem>();
                
                ConfigureShockwave(shockwavePS, radius, duration);
                
                // Create debris particles
                GameObject debrisGO = new GameObject("Debris");
                debrisGO.transform.SetParent(explosionGO.transform);
                ParticleSystem debrisPS = debrisGO.AddComponent<ParticleSystem>();
                
                ConfigureDebris(debrisPS, force, radius);
                
                // Create smoke effect
                GameObject smokeGO = new GameObject("Smoke");
                smokeGO.transform.SetParent(explosionGO.transform);
                ParticleSystem smokePS = smokeGO.AddComponent<ParticleSystem>();
                
                ConfigureSmoke(smokePS, radius, duration);
                
                // Create flash effect
                GameObject flashGO = new GameObject("Flash");
                flashGO.transform.SetParent(explosionGO.transform);
                Light flashLight = flashGO.AddComponent<Light>();
                
                ConfigureFlash(flashLight, radius, duration);
                
                // Add audio source for explosion sound
                AudioSource audioSource = explosionGO.AddComponent<AudioSource>();
                ConfigureExplosionAudio(audioSource);
                
                Selection.activeGameObject = explosionGO;
                
                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = explosionGO.name,
                    ["instanceId"] = explosionGO.GetInstanceID(),
                    ["explosionType"] = explosionType,
                    ["force"] = force,
                    ["radius"] = radius,
                    ["duration"] = duration
                };

                return JsonHelper.CreateSuccessResponse("Explosion effect created successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateExplosionEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating explosion effect: {ex.Message}");
            }
        }
        
        private static void ConfigureMainExplosion(ParticleSystem ps, string explosionType, float force, float radius, float duration)
        {
            var main = ps.main;
            var emission = ps.emission;
            var shape = ps.shape;
            var velocity = ps.velocityOverLifetime;
            var sizeOverLifetime = ps.sizeOverLifetime;
            var colorOverLifetime = ps.colorOverLifetime;
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            
            // Configure main module
            main.startLifetime = new ParticleSystem.MinMaxCurve(0.5f * duration, 1.0f * duration);
            main.startSpeed = new ParticleSystem.MinMaxCurve(force * 0.5f, force * 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.2f, 0.8f);
            main.maxParticles = Mathf.RoundToInt(100 * force);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            // Set color based on explosion type
            switch (explosionType.ToLower())
            {
                case "fire":
                    main.startColor = new Color(1.0f, 0.4f, 0.0f, 1.0f);
                    break;
                case "ice":
                    main.startColor = new Color(0.7f, 0.9f, 1.0f, 1.0f);
                    break;
                case "electric":
                    main.startColor = new Color(0.3f, 0.7f, 1.0f, 1.0f);
                    break;
                default:
                    main.startColor = new Color(1.0f, 0.6f, 0.2f, 1.0f);
                    break;
            }
            
            // Configure emission
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, Mathf.RoundToInt(50 * force))
            });
            
            // Configure shape
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.1f;
            
            // Configure velocity
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.radial = new ParticleSystem.MinMaxCurve(force, force * 2.0f);
            
            // Configure size over lifetime
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.2f);
            sizeCurve.AddKey(0.3f, 1.0f);
            sizeCurve.AddKey(1f, 0.1f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, sizeCurve);
            
            // Configure color over lifetime
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            
            GradientColorKey[] colorKeys;
            GradientAlphaKey[] alphaKeys = new GradientAlphaKey[3];
            alphaKeys[0] = new GradientAlphaKey(1.0f, 0.0f);
            alphaKeys[1] = new GradientAlphaKey(0.8f, 0.5f);
            alphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            switch (explosionType.ToLower())
            {
                case "fire":
                    colorKeys = new GradientColorKey[3];
                    colorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 0.8f), 0.0f);
                    colorKeys[1] = new GradientColorKey(new Color(1.0f, 0.4f, 0.0f), 0.5f);
                    colorKeys[2] = new GradientColorKey(new Color(0.3f, 0.1f, 0.0f), 1.0f);
                    break;
                case "ice":
                    colorKeys = new GradientColorKey[3];
                    colorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 1.0f), 0.0f);
                    colorKeys[1] = new GradientColorKey(new Color(0.7f, 0.9f, 1.0f), 0.5f);
                    colorKeys[2] = new GradientColorKey(new Color(0.3f, 0.5f, 0.8f), 1.0f);
                    break;
                case "electric":
                    colorKeys = new GradientColorKey[3];
                    colorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 1.0f), 0.0f);
                    colorKeys[1] = new GradientColorKey(new Color(0.3f, 0.7f, 1.0f), 0.5f);
                    colorKeys[2] = new GradientColorKey(new Color(0.1f, 0.2f, 0.6f), 1.0f);
                    break;
                default:
                    colorKeys = new GradientColorKey[3];
                    colorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 0.8f), 0.0f);
                    colorKeys[1] = new GradientColorKey(new Color(1.0f, 0.6f, 0.2f), 0.5f);
                    colorKeys[2] = new GradientColorKey(new Color(0.5f, 0.2f, 0.1f), 1.0f);
                    break;
            }
            
            gradient.SetKeys(colorKeys, alphaKeys);
            colorOverLifetime.color = gradient;
            
            renderer.material = CreateExplosionMaterial("ExplosionMaterial");
            renderer.renderMode = ParticleSystemRenderMode.Billboard;
        }
        
        private static void ConfigureShockwave(ParticleSystem ps, float radius, float duration)
        {
            var main = ps.main;
            var emission = ps.emission;
            var shape = ps.shape;
            var sizeOverLifetime = ps.sizeOverLifetime;
            var colorOverLifetime = ps.colorOverLifetime;
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration * 0.5f;
            main.startSpeed = 0.1f;
            main.startSize = 0.1f;
            main.startColor = new Color(1.0f, 0.8f, 0.6f, 0.3f);
            main.maxParticles = 5;
            main.simulationSpace = ParticleSystemSimulationSpace.Local;
            
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 3)
            });
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.1f;
            
            // Expanding shockwave effect
            sizeOverLifetime.enabled = true;
            AnimationCurve shockwaveSizeCurve = new AnimationCurve();
            shockwaveSizeCurve.AddKey(0f, 0.1f);
            shockwaveSizeCurve.AddKey(1f, radius * 2.0f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, shockwaveSizeCurve);
            
            colorOverLifetime.enabled = true;
            Gradient shockwaveGradient = new Gradient();
            GradientAlphaKey[] shockwaveAlphaKeys = new GradientAlphaKey[3];
            shockwaveAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            shockwaveAlphaKeys[1] = new GradientAlphaKey(0.5f, 0.2f);
            shockwaveAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            GradientColorKey[] shockwaveColorKeys = new GradientColorKey[1];
            shockwaveColorKeys[0] = new GradientColorKey(new Color(1.0f, 0.8f, 0.6f), 0.0f);
            
            shockwaveGradient.SetKeys(shockwaveColorKeys, shockwaveAlphaKeys);
            colorOverLifetime.color = shockwaveGradient;
            
            renderer.material = CreateShockwaveMaterial("ShockwaveMaterial");
            renderer.renderMode = ParticleSystemRenderMode.HorizontalBillboard;
        }
        
        private static void ConfigureDebris(ParticleSystem ps, float force, float radius)
        {
            var main = ps.main;
            var emission = ps.emission;
            var shape = ps.shape;
            var velocity = ps.velocityOverLifetime;
            var gravity = ps.forceOverLifetime;
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startSpeed = new ParticleSystem.MinMaxCurve(force * 0.3f, force * 1.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.2f);
            main.startColor = new Color(0.4f, 0.3f, 0.2f, 1.0f);
            main.maxParticles = Mathf.RoundToInt(50 * force);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, Mathf.RoundToInt(30 * force))
            });
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.2f;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.radial = new ParticleSystem.MinMaxCurve(force * 0.5f, force * 2.0f);
            
            gravity.enabled = true;
            gravity.space = ParticleSystemSimulationSpace.World;
            gravity.y = new ParticleSystem.MinMaxCurve(-9.81f);
            
            renderer.material = CreateDebrisMaterial("DebrisMaterial");
            renderer.renderMode = ParticleSystemRenderMode.Billboard;
        }
        
        private static void ConfigureSmoke(ParticleSystem ps, float radius, float duration)
        {
            var main = ps.main;
            var emission = ps.emission;
            var shape = ps.shape;
            var velocity = ps.velocityOverLifetime;
            var sizeOverLifetime = ps.sizeOverLifetime;
            var colorOverLifetime = ps.colorOverLifetime;
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration * 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            main.startColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            main.maxParticles = Mathf.RoundToInt(100 * radius);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            emission.rateOverTime = 20 * radius;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = radius * 0.3f;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.y = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            
            sizeOverLifetime.enabled = true;
            AnimationCurve smokeSizeCurve = new AnimationCurve();
            smokeSizeCurve.AddKey(0f, 0.3f);
            smokeSizeCurve.AddKey(0.5f, 1.0f);
            smokeSizeCurve.AddKey(1f, 1.5f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, smokeSizeCurve);
            
            colorOverLifetime.enabled = true;
            Gradient smokeGradient = new Gradient();
            GradientColorKey[] smokeColorKeys = new GradientColorKey[2];
            smokeColorKeys[0] = new GradientColorKey(new Color(0.3f, 0.3f, 0.3f), 0.0f);
            smokeColorKeys[1] = new GradientColorKey(new Color(0.6f, 0.6f, 0.6f), 1.0f);
            
            GradientAlphaKey[] smokeAlphaKeys = new GradientAlphaKey[3];
            smokeAlphaKeys[0] = new GradientAlphaKey(0.8f, 0.0f);
            smokeAlphaKeys[1] = new GradientAlphaKey(0.6f, 0.5f);
            smokeAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            smokeGradient.SetKeys(smokeColorKeys, smokeAlphaKeys);
            colorOverLifetime.color = smokeGradient;
            
            renderer.material = CreateSmokeMaterial("SmokeMaterial");
            renderer.renderMode = ParticleSystemRenderMode.Billboard;
        }
        
        private static void ConfigureFlash(Light light, float radius, float duration)
        {
            light.type = LightType.Point;
            light.color = new Color(1.0f, 0.8f, 0.6f, 1.0f);
            light.intensity = 8.0f;
            light.range = radius * 2.0f;
            light.shadows = LightShadows.Soft;
            
            // Create animation to fade out the light
            AnimationClip clip = new AnimationClip();
            clip.name = "ExplosionFlash";
            
            AnimationCurve intensityCurve = new AnimationCurve();
            intensityCurve.AddKey(0f, 8.0f);
            intensityCurve.AddKey(0.1f, 12.0f);
            intensityCurve.AddKey(duration * 0.5f, 2.0f);
            intensityCurve.AddKey(duration, 0.0f);
            
            clip.SetCurve("", typeof(Light), "m_Intensity", intensityCurve);
            
            Animation animation = light.gameObject.AddComponent<Animation>();
            animation.AddClip(clip, "ExplosionFlash");
            animation.Play("ExplosionFlash");
        }
        
        private static void ConfigureExplosionAudio(AudioSource audioSource)
        {
            audioSource.volume = 0.8f;
            audioSource.pitch = UnityEngine.Random.Range(0.8f, 1.2f);
            audioSource.spatialBlend = 1.0f; // 3D sound
            audioSource.rolloffMode = AudioRolloffMode.Logarithmic;
            audioSource.maxDistance = 50.0f;
            audioSource.playOnAwake = true;
        }
        
        private static Material CreateExplosionMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for additive blending
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateShockwaveMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            // Configure material for alpha blending
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateDebrisMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Standard"));
            material.name = materialName;
            
            // Configure material for opaque rendering
            material.SetFloat("_Metallic", 0.0f);
            material.SetFloat("_Glossiness", 0.2f);
            material.SetColor("_Color", new Color(0.4f, 0.3f, 0.2f, 1.0f));
            
            return material;
        }
        
        private static string ModifyExplosionEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for modification");
                }

                GameObject explosionGO = GameObject.Find(gameObjectName);
                if (explosionGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Explosion effect GameObject '{gameObjectName}' not found");
                }

                // Modify force if provided
                if (parameters["force"] != null)
                {
                    float newForce = parameters["force"].Value<float>();
                    
                    // Update particle systems based on new force
                    ParticleSystem[] particleSystems = explosionGO.GetComponentsInChildren<ParticleSystem>();
                    foreach (ParticleSystem ps in particleSystems)
                    {
                        var main = ps.main;
                        main.startSpeed = new ParticleSystem.MinMaxCurve(newForce * 0.5f, newForce * 2.0f);
                        main.maxParticles = Mathf.RoundToInt(100 * newForce);
                    }
                }

                return JsonHelper.CreateSuccessResponse("Explosion effect modified successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ModifyExplosionEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error modifying explosion effect: {ex.Message}");
            }
        }
        
        private static string DeleteExplosionEffect(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for deletion");
                }

                GameObject explosionGO = GameObject.Find(gameObjectName);
                if (explosionGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Explosion effect GameObject '{gameObjectName}' not found");
                }

                UnityEngine.Object.DestroyImmediate(explosionGO);
                
                return JsonHelper.CreateSuccessResponse($"Explosion effect '{gameObjectName}' deleted successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"DeleteExplosionEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error deleting explosion effect: {ex.Message}");
            }
        }
        
        private static string GetExplosionEffectProperties(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject explosionGO = GameObject.Find(gameObjectName);
                if (explosionGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Explosion effect GameObject '{gameObjectName}' not found");
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = explosionGO.name,
                    ["position"] = new float[] { explosionGO.transform.position.x, explosionGO.transform.position.y, explosionGO.transform.position.z },
                    ["rotation"] = new float[] { explosionGO.transform.rotation.x, explosionGO.transform.rotation.y, explosionGO.transform.rotation.z, explosionGO.transform.rotation.w },
                    ["scale"] = new float[] { explosionGO.transform.localScale.x, explosionGO.transform.localScale.y, explosionGO.transform.localScale.z }
                };
                
                // Get particle systems info
                ParticleSystem[] particleSystems = explosionGO.GetComponentsInChildren<ParticleSystem>();
                var particleSystemsInfo = new List<Dictionary<string, object>>();
                
                foreach (ParticleSystem ps in particleSystems)
                {
                    particleSystemsInfo.Add(new Dictionary<string, object>
                    {
                        ["name"] = ps.gameObject.name,
                        ["isPlaying"] = ps.isPlaying,
                        ["particleCount"] = ps.particleCount,
                        ["maxParticles"] = ps.main.maxParticles,
                        ["emissionRate"] = ps.emission.rateOverTime.constant
                    });
                }
                
                result["particleSystems"] = particleSystemsInfo;
                
                // Get light info
                Light light = explosionGO.GetComponentInChildren<Light>();
                if (light != null)
                {
                    result["light"] = new Dictionary<string, object>
                    {
                        ["intensity"] = light.intensity,
                        ["range"] = light.range,
                        ["color"] = new float[] { light.color.r, light.color.g, light.color.b, light.color.a }
                    };
                }
                
                // Get audio info
                AudioSource audioSource = explosionGO.GetComponent<AudioSource>();
                if (audioSource != null)
                {
                    result["audio"] = new Dictionary<string, object>
                    {
                        ["volume"] = audioSource.volume,
                        ["pitch"] = audioSource.pitch,
                        ["isPlaying"] = audioSource.isPlaying
                    };
                }

                return JsonHelper.CreateSuccessResponse("Explosion effect properties retrieved successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"GetExplosionEffectProperties error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error getting explosion effect properties: {ex.Message}");
            }
        }

        private static string SetupMagicEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();

                switch (action?.ToLower())
                {
                    case "create":
                        return CreateMagicEffect(parameters);
                    case "modify":
                        return ModifyMagicEffect(parameters);
                    case "delete":
                        return DeleteMagicEffect(gameObjectName);
                    case "get_properties":
                        return GetMagicEffectProperties(gameObjectName);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown magic effect action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"SetupMagicEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error setting up magic effects: {ex.Message}");
            }
        }
        
        private static string CreateMagicEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString() ?? "MagicEffect";
                string magicType = parameters["magic_type"]?.ToString() ?? "energy";
                float intensity = parameters["intensity"]?.Value<float>() ?? 1.0f;
                float duration = parameters["duration"]?.Value<float>() ?? 3.0f;
                
                GameObject magicGO = new GameObject(gameObjectName);
                
                switch (magicType.ToLower())
                {
                    case "energy":
                        CreateEnergyMagicEffect(magicGO, intensity, duration);
                        break;
                    case "fire":
                        CreateFireMagicEffect(magicGO, intensity, duration);
                        break;
                    case "ice":
                        CreateIceMagicEffect(magicGO, intensity, duration);
                        break;
                    case "lightning":
                        CreateLightningMagicEffect(magicGO, intensity, duration);
                        break;
                    case "healing":
                        CreateHealingMagicEffect(magicGO, intensity, duration);
                        break;
                    case "dark":
                        CreateDarkMagicEffect(magicGO, intensity, duration);
                        break;
                    case "teleport":
                        CreateTeleportMagicEffect(magicGO, intensity, duration);
                        break;
                    case "shield":
                        CreateShieldMagicEffect(magicGO, intensity, duration);
                        break;
                    default:
                        CreateEnergyMagicEffect(magicGO, intensity, duration);
                        break;
                }
                
                Selection.activeGameObject = magicGO;
                
                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = magicGO.name,
                    ["instanceId"] = magicGO.GetInstanceID(),
                    ["magicType"] = magicType,
                    ["intensity"] = intensity,
                    ["duration"] = duration
                };

                return JsonHelper.CreateSuccessResponse("Magic effect created successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateMagicEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating magic effect: {ex.Message}");
            }
        }
        
        private static void CreateEnergyMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create main energy orb
            GameObject orbGO = new GameObject("EnergyOrb");
            orbGO.transform.SetParent(parent.transform);
            ParticleSystem orbPS = orbGO.AddComponent<ParticleSystem>();
            
            var main = orbPS.main;
            var emission = orbPS.emission;
            var shape = orbPS.shape;
            var colorOverLifetime = orbPS.colorOverLifetime;
            var sizeOverLifetime = orbPS.sizeOverLifetime;
            var renderer = orbPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration;
            main.startSpeed = 0.5f;
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startColor = new Color(0.3f, 0.7f, 1.0f, 0.8f);
            main.maxParticles = Mathf.RoundToInt(50 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.Local;
            
            emission.rateOverTime = 20 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.5f * intensity;
            
            // Pulsing energy effect
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.5f, 1.0f);
            sizeCurve.AddKey(1f, 0.3f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1.0f, sizeCurve);
            
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            GradientColorKey[] colorKeys = new GradientColorKey[3];
            colorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 1.0f), 0.0f);
            colorKeys[1] = new GradientColorKey(new Color(0.3f, 0.7f, 1.0f), 0.5f);
            colorKeys[2] = new GradientColorKey(new Color(0.1f, 0.3f, 0.8f), 1.0f);
            
            GradientAlphaKey[] alphaKeys = new GradientAlphaKey[3];
            alphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            alphaKeys[1] = new GradientAlphaKey(0.8f, 0.3f);
            alphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            gradient.SetKeys(colorKeys, alphaKeys);
            colorOverLifetime.color = gradient;
            
            renderer.material = CreateEnergyMaterial("EnergyMaterial");
            
            // Add energy sparks
            CreateEnergySparks(parent, intensity, duration);
            
            // Add light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.3f, 0.7f, 1.0f);
            light.intensity = 2.0f * intensity;
            light.range = 3.0f * intensity;
        }
        
        private static void CreateEnergySparks(GameObject parent, float intensity, float duration)
        {
            GameObject sparksGO = new GameObject("EnergySparks");
            sparksGO.transform.SetParent(parent.transform);
            ParticleSystem sparksPS = sparksGO.AddComponent<ParticleSystem>();
            
            var main = sparksPS.main;
            var emission = sparksPS.emission;
            var shape = sparksPS.shape;
            var velocity = sparksPS.velocityOverLifetime;
            var renderer = sparksPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            main.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.05f);
            main.startColor = new Color(1.0f, 1.0f, 0.8f, 1.0f);
            main.maxParticles = Mathf.RoundToInt(100 * intensity);
            
            emission.rateOverTime = 30 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.3f;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.radial = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            
            renderer.material = CreateSparkMaterial("SparkMaterial");
        }
        
        private static void CreateFireMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create fire orb
            GameObject fireGO = new GameObject("FireOrb");
            fireGO.transform.SetParent(parent.transform);
            ParticleSystem firePS = fireGO.AddComponent<ParticleSystem>();
            
            var main = firePS.main;
            var emission = firePS.emission;
            var shape = firePS.shape;
            var colorOverLifetime = firePS.colorOverLifetime;
            var renderer = firePS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration * 0.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.2f, 0.5f);
            main.startColor = new Color(1.0f, 0.4f, 0.0f, 0.8f);
            main.maxParticles = Mathf.RoundToInt(80 * intensity);
            
            emission.rateOverTime = 40 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.3f * intensity;
            
            colorOverLifetime.enabled = true;
            Gradient fireGradient = new Gradient();
            GradientColorKey[] fireColorKeys = new GradientColorKey[3];
            fireColorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 0.8f), 0.0f);
            fireColorKeys[1] = new GradientColorKey(new Color(1.0f, 0.4f, 0.0f), 0.5f);
            fireColorKeys[2] = new GradientColorKey(new Color(0.3f, 0.1f, 0.0f), 1.0f);
            
            GradientAlphaKey[] fireAlphaKeys = new GradientAlphaKey[2];
            fireAlphaKeys[0] = new GradientAlphaKey(0.8f, 0.0f);
            fireAlphaKeys[1] = new GradientAlphaKey(0.0f, 1.0f);
            
            fireGradient.SetKeys(fireColorKeys, fireAlphaKeys);
            colorOverLifetime.color = fireGradient;
            
            renderer.material = CreateFireMaterial("FireMagicMaterial");
            
            // Add warm light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(1.0f, 0.6f, 0.2f);
            light.intensity = 3.0f * intensity;
            light.range = 4.0f * intensity;
        }
        
        private static void CreateIceMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create ice crystals
            GameObject iceGO = new GameObject("IceCrystals");
            iceGO.transform.SetParent(parent.transform);
            ParticleSystem icePS = iceGO.AddComponent<ParticleSystem>();
            
            var main = icePS.main;
            var emission = icePS.emission;
            var shape = icePS.shape;
            var velocity = icePS.velocityOverLifetime;
            var colorOverLifetime = icePS.colorOverLifetime;
            var renderer = icePS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.2f, 1.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.4f);
            main.startColor = new Color(0.7f, 0.9f, 1.0f, 0.9f);
            main.maxParticles = Mathf.RoundToInt(60 * intensity);
            
            emission.rateOverTime = 25 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.5f * intensity;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.y = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            colorOverLifetime.enabled = true;
            Gradient iceGradient = new Gradient();
            GradientColorKey[] iceColorKeys = new GradientColorKey[2];
            iceColorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 1.0f), 0.0f);
            iceColorKeys[1] = new GradientColorKey(new Color(0.7f, 0.9f, 1.0f), 1.0f);
            
            GradientAlphaKey[] iceAlphaKeys = new GradientAlphaKey[3];
            iceAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            iceAlphaKeys[1] = new GradientAlphaKey(0.9f, 0.3f);
            iceAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            iceGradient.SetKeys(iceColorKeys, iceAlphaKeys);
            colorOverLifetime.color = iceGradient;
            
            renderer.material = CreateIceMaterial("IceMagicMaterial");
            
            // Add cold light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.7f, 0.9f, 1.0f);
            light.intensity = 2.5f * intensity;
            light.range = 3.5f * intensity;
        }
        
        private static void CreateLightningMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create lightning sparks
            GameObject lightningGO = new GameObject("Lightning");
            lightningGO.transform.SetParent(parent.transform);
            ParticleSystem lightningPS = lightningGO.AddComponent<ParticleSystem>();
            
            var main = lightningPS.main;
            var emission = lightningPS.emission;
            var shape = lightningPS.shape;
            var velocity = lightningPS.velocityOverLifetime;
            var renderer = lightningPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startSpeed = new ParticleSystem.MinMaxCurve(5.0f, 15.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.15f);
            main.startColor = new Color(0.8f, 0.9f, 1.0f, 1.0f);
            main.maxParticles = Mathf.RoundToInt(200 * intensity);
            
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, Mathf.RoundToInt(50 * intensity)),
                new ParticleSystem.Burst(0.2f, Mathf.RoundToInt(30 * intensity)),
                new ParticleSystem.Burst(0.5f, Mathf.RoundToInt(40 * intensity))
            });
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.1f;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.radial = new ParticleSystem.MinMaxCurve(3.0f, 8.0f);
            
            renderer.material = CreateLightningMaterial("LightningMagicMaterial");
            
            // Add bright light with flicker
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.8f, 0.9f, 1.0f);
            light.intensity = 5.0f * intensity;
            light.range = 5.0f * intensity;
        }
        
        private static void CreateHealingMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create healing sparkles
            GameObject healingGO = new GameObject("HealingSparkles");
            healingGO.transform.SetParent(parent.transform);
            ParticleSystem healingPS = healingGO.AddComponent<ParticleSystem>();
            
            var main = healingPS.main;
            var emission = healingPS.emission;
            var shape = healingPS.shape;
            var velocity = healingPS.velocityOverLifetime;
            var colorOverLifetime = healingPS.colorOverLifetime;
            var renderer = healingPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.2f);
            main.startColor = new Color(0.2f, 1.0f, 0.3f, 0.8f);
            main.maxParticles = Mathf.RoundToInt(100 * intensity);
            
            emission.rateOverTime = 30 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.8f * intensity;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.y = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            
            colorOverLifetime.enabled = true;
            Gradient healingGradient = new Gradient();
            GradientColorKey[] healingColorKeys = new GradientColorKey[3];
            healingColorKeys[0] = new GradientColorKey(new Color(1.0f, 1.0f, 0.8f), 0.0f);
            healingColorKeys[1] = new GradientColorKey(new Color(0.2f, 1.0f, 0.3f), 0.5f);
            healingColorKeys[2] = new GradientColorKey(new Color(0.1f, 0.6f, 0.2f), 1.0f);
            
            GradientAlphaKey[] healingAlphaKeys = new GradientAlphaKey[3];
            healingAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            healingAlphaKeys[1] = new GradientAlphaKey(0.8f, 0.3f);
            healingAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            healingGradient.SetKeys(healingColorKeys, healingAlphaKeys);
            colorOverLifetime.color = healingGradient;
            
            renderer.material = CreateHealingMaterial("HealingMagicMaterial");
            
            // Add soft green light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.2f, 1.0f, 0.3f);
            light.intensity = 2.0f * intensity;
            light.range = 4.0f * intensity;
        }
        
        private static void CreateDarkMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create dark energy
            GameObject darkGO = new GameObject("DarkEnergy");
            darkGO.transform.SetParent(parent.transform);
            ParticleSystem darkPS = darkGO.AddComponent<ParticleSystem>();
            
            var main = darkPS.main;
            var emission = darkPS.emission;
            var shape = darkPS.shape;
            var velocity = darkPS.velocityOverLifetime;
            var colorOverLifetime = darkPS.colorOverLifetime;
            var renderer = darkPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.3f, 1.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.2f, 0.6f);
            main.startColor = new Color(0.3f, 0.1f, 0.5f, 0.7f);
            main.maxParticles = Mathf.RoundToInt(70 * intensity);
            
            emission.rateOverTime = 25 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.4f * intensity;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.radial = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            colorOverLifetime.enabled = true;
            Gradient darkGradient = new Gradient();
            GradientColorKey[] darkColorKeys = new GradientColorKey[3];
            darkColorKeys[0] = new GradientColorKey(new Color(0.6f, 0.3f, 0.8f), 0.0f);
            darkColorKeys[1] = new GradientColorKey(new Color(0.3f, 0.1f, 0.5f), 0.5f);
            darkColorKeys[2] = new GradientColorKey(new Color(0.1f, 0.0f, 0.2f), 1.0f);
            
            GradientAlphaKey[] darkAlphaKeys = new GradientAlphaKey[3];
            darkAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            darkAlphaKeys[1] = new GradientAlphaKey(0.7f, 0.4f);
            darkAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            darkGradient.SetKeys(darkColorKeys, darkAlphaKeys);
            colorOverLifetime.color = darkGradient;
            
            renderer.material = CreateDarkMaterial("DarkMagicMaterial");
            
            // Add dim purple light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.3f, 0.1f, 0.5f);
            light.intensity = 1.5f * intensity;
            light.range = 3.0f * intensity;
        }
        
        private static void CreateTeleportMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create teleport swirl
            GameObject teleportGO = new GameObject("TeleportSwirl");
            teleportGO.transform.SetParent(parent.transform);
            ParticleSystem teleportPS = teleportGO.AddComponent<ParticleSystem>();
            
            var main = teleportPS.main;
            var emission = teleportPS.emission;
            var shape = teleportPS.shape;
            var velocity = teleportPS.velocityOverLifetime;
            var colorOverLifetime = teleportPS.colorOverLifetime;
            var renderer = teleportPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration * 0.8f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startColor = new Color(0.8f, 0.4f, 1.0f, 0.8f);
            main.maxParticles = Mathf.RoundToInt(120 * intensity);
            
            emission.rateOverTime = 50 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.1f;
            
            velocity.enabled = true;
            velocity.space = ParticleSystemSimulationSpace.Local;
            velocity.radial = new ParticleSystem.MinMaxCurve(2.0f, 4.0f);
            velocity.y = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            
            colorOverLifetime.enabled = true;
            Gradient teleportGradient = new Gradient();
            GradientColorKey[] teleportColorKeys = new GradientColorKey[3];
            teleportColorKeys[0] = new GradientColorKey(new Color(1.0f, 0.8f, 1.0f), 0.0f);
            teleportColorKeys[1] = new GradientColorKey(new Color(0.8f, 0.4f, 1.0f), 0.5f);
            teleportColorKeys[2] = new GradientColorKey(new Color(0.4f, 0.2f, 0.6f), 1.0f);
            
            GradientAlphaKey[] teleportAlphaKeys = new GradientAlphaKey[3];
            teleportAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            teleportAlphaKeys[1] = new GradientAlphaKey(0.8f, 0.3f);
            teleportAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            teleportGradient.SetKeys(teleportColorKeys, teleportAlphaKeys);
            colorOverLifetime.color = teleportGradient;
            
            renderer.material = CreateTeleportMaterial("TeleportMagicMaterial");
            
            // Add magical light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.8f, 0.4f, 1.0f);
            light.intensity = 3.5f * intensity;
            light.range = 4.5f * intensity;
        }
        
        private static void CreateShieldMagicEffect(GameObject parent, float intensity, float duration)
        {
            // Create shield barrier
            GameObject shieldGO = new GameObject("ShieldBarrier");
            shieldGO.transform.SetParent(parent.transform);
            ParticleSystem shieldPS = shieldGO.AddComponent<ParticleSystem>();
            
            var main = shieldPS.main;
            var emission = shieldPS.emission;
            var shape = shieldPS.shape;
            var colorOverLifetime = shieldPS.colorOverLifetime;
            var renderer = shieldPS.GetComponent<ParticleSystemRenderer>();
            
            main.startLifetime = duration;
            main.startSpeed = 0.1f;
            main.startSize = new ParticleSystem.MinMaxCurve(0.5f, 1.0f);
            main.startColor = new Color(0.2f, 0.6f, 1.0f, 0.6f);
            main.maxParticles = Mathf.RoundToInt(30 * intensity);
            
            emission.rateOverTime = 10 * intensity;
            
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Hemisphere;
            shape.radius = 1.5f * intensity;
            
            colorOverLifetime.enabled = true;
            Gradient shieldGradient = new Gradient();
            GradientColorKey[] shieldColorKeys = new GradientColorKey[2];
            shieldColorKeys[0] = new GradientColorKey(new Color(0.4f, 0.8f, 1.0f), 0.0f);
            shieldColorKeys[1] = new GradientColorKey(new Color(0.2f, 0.6f, 1.0f), 1.0f);
            
            GradientAlphaKey[] shieldAlphaKeys = new GradientAlphaKey[3];
            shieldAlphaKeys[0] = new GradientAlphaKey(0.0f, 0.0f);
            shieldAlphaKeys[1] = new GradientAlphaKey(0.6f, 0.5f);
            shieldAlphaKeys[2] = new GradientAlphaKey(0.0f, 1.0f);
            
            shieldGradient.SetKeys(shieldColorKeys, shieldAlphaKeys);
            colorOverLifetime.color = shieldGradient;
            
            renderer.material = CreateShieldMaterial("ShieldMagicMaterial");
            
            // Add protective light
            Light light = parent.AddComponent<Light>();
            light.type = LightType.Point;
            light.color = new Color(0.2f, 0.6f, 1.0f);
            light.intensity = 2.5f * intensity;
            light.range = 5.0f * intensity;
        }
        
        // Material creation methods for magic effects
        private static Material CreateEnergyMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateIceMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Standard"));
            material.name = materialName;
            
            material.SetFloat("_Mode", 3); // Transparent mode
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            material.SetFloat("_Metallic", 0.0f);
            material.SetFloat("_Glossiness", 0.9f);
            
            return material;
        }
        
        private static Material CreateLightningMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateHealingMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateDarkMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateTeleportMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = materialName;
            
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            return material;
        }
        
        private static Material CreateShieldMaterial(string materialName)
        {
            Material material = new Material(Shader.Find("Standard"));
            material.name = materialName;
            
            material.SetFloat("_Mode", 3); // Transparent mode
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            material.SetFloat("_Metallic", 0.2f);
            material.SetFloat("_Glossiness", 0.8f);
            
            return material;
        }
        
        private static string ModifyMagicEffect(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for modification");
                }

                GameObject magicGO = GameObject.Find(gameObjectName);
                if (magicGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Magic effect GameObject '{gameObjectName}' not found");
                }

                // Modify intensity if provided
                if (parameters["intensity"] != null)
                {
                    float newIntensity = parameters["intensity"].Value<float>();
                    
                    // Update particle systems based on new intensity
                    ParticleSystem[] particleSystems = magicGO.GetComponentsInChildren<ParticleSystem>();
                    foreach (ParticleSystem ps in particleSystems)
                    {
                        var emission = ps.emission;
                        emission.rateOverTime = emission.rateOverTime.constant * newIntensity;
                    }
                    
                    // Update light intensity
                    Light light = magicGO.GetComponent<Light>();
                    if (light != null)
                    {
                        light.intensity *= newIntensity;
                    }
                }

                return JsonHelper.CreateSuccessResponse("Magic effect modified successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ModifyMagicEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error modifying magic effect: {ex.Message}");
            }
        }
        
        private static string DeleteMagicEffect(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required for deletion");
                }

                GameObject magicGO = GameObject.Find(gameObjectName);
                if (magicGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Magic effect GameObject '{gameObjectName}' not found");
                }

                UnityEngine.Object.DestroyImmediate(magicGO);
                
                return JsonHelper.CreateSuccessResponse($"Magic effect '{gameObjectName}' deleted successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"DeleteMagicEffect error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error deleting magic effect: {ex.Message}");
            }
        }
        
        private static string GetMagicEffectProperties(string gameObjectName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return JsonHelper.CreateErrorResponse("GameObject name is required");
                }

                GameObject magicGO = GameObject.Find(gameObjectName);
                if (magicGO == null)
                {
                    return JsonHelper.CreateErrorResponse($"Magic effect GameObject '{gameObjectName}' not found");
                }

                var result = new Dictionary<string, object>
                {
                    ["gameObject"] = magicGO.name,
                    ["position"] = new float[] { magicGO.transform.position.x, magicGO.transform.position.y, magicGO.transform.position.z },
                    ["rotation"] = new float[] { magicGO.transform.rotation.x, magicGO.transform.rotation.y, magicGO.transform.rotation.z, magicGO.transform.rotation.w },
                    ["scale"] = new float[] { magicGO.transform.localScale.x, magicGO.transform.localScale.y, magicGO.transform.localScale.z }
                };
                
                // Get particle systems info
                ParticleSystem[] particleSystems = magicGO.GetComponentsInChildren<ParticleSystem>();
                var particleSystemsInfo = new List<Dictionary<string, object>>();
                
                foreach (ParticleSystem ps in particleSystems)
                {
                    particleSystemsInfo.Add(new Dictionary<string, object>
                    {
                        ["name"] = ps.gameObject.name,
                        ["isPlaying"] = ps.isPlaying,
                        ["particleCount"] = ps.particleCount,
                        ["maxParticles"] = ps.main.maxParticles,
                        ["emissionRate"] = ps.emission.rateOverTime.constant
                    });
                }
                
                result["particleSystems"] = particleSystemsInfo;
                
                // Get light info
                Light light = magicGO.GetComponent<Light>();
                if (light != null)
                {
                    result["light"] = new Dictionary<string, object>
                    {
                        ["intensity"] = light.intensity,
                        ["range"] = light.range,
                        ["color"] = new float[] { light.color.r, light.color.g, light.color.b, light.color.a }
                    };
                }

                return JsonHelper.CreateSuccessResponse("Magic effect properties retrieved successfully", result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"GetMagicEffectProperties error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error getting magic effect properties: {ex.Message}");
            }
        }

        private static string CreateWeatherEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateWeatherEffect(parameters);
                    case "modify":
                        return ModifyWeatherEffect(parameters);
                    case "delete":
                        return DeleteWeatherEffect(parameters);
                    case "get_properties":
                        return GetWeatherEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateWeatherEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating weather effects: {ex.Message}");
            }
        }
        
        private static string CreateWeatherEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "WeatherEffect";
            string weatherType = parameters["weather_type"]?.ToString() ?? "rain";
            Vector3 position = JsonHelper.ParseVector3(parameters["position"], Vector3.zero);
            Vector3 scale = JsonHelper.ParseVector3(parameters["scale"], Vector3.one);
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            
            GameObject weatherEffect = new GameObject(effectName);
            weatherEffect.transform.position = position;
            weatherEffect.transform.localScale = scale;
            
            switch (weatherType.ToLower())
            {
                case "rain":
                    CreateRainWeatherEffect(weatherEffect, intensity);
                    break;
                case "snow":
                    CreateSnowWeatherEffect(weatherEffect, intensity);
                    break;
                case "fog":
                    CreateFogWeatherEffect(weatherEffect, intensity);
                    break;
                case "wind":
                    CreateWindWeatherEffect(weatherEffect, intensity);
                    break;
                case "storm":
                    CreateStormWeatherEffect(weatherEffect, intensity);
                    break;
                case "lightning":
                    CreateLightningWeatherEffect(weatherEffect, intensity);
                    break;
                default:
                    CreateRainWeatherEffect(weatherEffect, intensity);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"Weather effect '{effectName}' of type '{weatherType}' created successfully");
        }
        
        private static void CreateRainWeatherEffect(GameObject parent, float intensity)
        {
            // Rain particles
            GameObject rainObj = new GameObject("Rain");
            rainObj.transform.SetParent(parent.transform);
            ParticleSystem rainPS = rainObj.AddComponent<ParticleSystem>();
            
            var main = rainPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = 10.0f * intensity;
            main.startSize = 0.1f;
            main.startColor = new Color(0.7f, 0.8f, 1.0f, 0.8f);
            main.maxParticles = (int)(1000 * intensity);
            
            var emission = rainPS.emission;
            emission.rateOverTime = 500 * intensity;
            
            var shape = rainPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(20, 0.1f, 20);
            
            var velocityOverLifetime = rainPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-15.0f * intensity);
            
            var sizeOverLifetime = rainPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 1f);
            sizeCurve.AddKey(1f, 0.5f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Rain material
            Material rainMaterial = CreateRainMaterial();
            var renderer = rainPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = rainMaterial;
            
            // Splash particles
            GameObject splashObj = new GameObject("RainSplash");
            splashObj.transform.SetParent(parent.transform);
            ParticleSystem splashPS = splashObj.AddComponent<ParticleSystem>();
            
            var splashMain = splashPS.main;
            splashMain.startLifetime = 0.5f;
            splashMain.startSpeed = 2.0f;
            splashMain.startSize = 0.05f;
            splashMain.startColor = new Color(0.8f, 0.9f, 1.0f, 0.6f);
            splashMain.maxParticles = (int)(200 * intensity);
            
            var splashEmission = splashPS.emission;
            splashEmission.rateOverTime = 100 * intensity;
            
            var splashShape = splashPS.shape;
            splashShape.enabled = true;
            splashShape.shapeType = ParticleSystemShapeType.Box;
            splashShape.scale = new Vector3(20, 0.1f, 20);
            
            // Audio
            AudioSource audioSource = parent.AddComponent<AudioSource>();
            audioSource.loop = true;
            audioSource.volume = 0.3f * intensity;
            audioSource.pitch = 1.0f;
        }
        
        private static void CreateSnowWeatherEffect(GameObject parent, float intensity)
        {
            // Snow particles
            GameObject snowObj = new GameObject("Snow");
            snowObj.transform.SetParent(parent.transform);
            ParticleSystem snowPS = snowObj.AddComponent<ParticleSystem>();
            
            var main = snowPS.main;
            main.startLifetime = 5.0f;
            main.startSpeed = 2.0f * intensity;
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.15f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, 0.9f);
            main.maxParticles = (int)(800 * intensity);
            
            var emission = snowPS.emission;
            emission.rateOverTime = 200 * intensity;
            
            var shape = snowPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(25, 0.1f, 25);
            
            var velocityOverLifetime = snowPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-3.0f * intensity);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var noise = snowPS.noise;
            noise.enabled = true;
            noise.strength = 0.5f;
            noise.frequency = 0.1f;
            
            // Snow material
            Material snowMaterial = CreateSnowMaterial();
            var renderer = snowPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = snowMaterial;
        }
        
        private static void CreateFogWeatherEffect(GameObject parent, float intensity)
        {
            // Fog particles
            GameObject fogObj = new GameObject("Fog");
            fogObj.transform.SetParent(parent.transform);
            ParticleSystem fogPS = fogObj.AddComponent<ParticleSystem>();
            
            var main = fogPS.main;
            main.startLifetime = 10.0f;
            main.startSpeed = 0.5f;
            main.startSize = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startColor = new Color(0.8f, 0.8f, 0.9f, 0.3f * intensity);
            main.maxParticles = (int)(300 * intensity);
            
            var emission = fogPS.emission;
            emission.rateOverTime = 50 * intensity;
            
            var shape = fogPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(30, 5, 30);
            
            var velocityOverLifetime = fogPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var sizeOverLifetime = fogPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 0.8f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Fog material
            Material fogMaterial = CreateFogMaterial();
            var renderer = fogPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = fogMaterial;
        }
        
        private static void CreateWindWeatherEffect(GameObject parent, float intensity)
        {
            // Wind zone
            WindZone windZone = parent.AddComponent<WindZone>();
            windZone.mode = WindZoneMode.Directional;
                            // Fixed: Correct WindZone properties for Unity 6.2
                windZone.windMain = 5.0f * intensity;
                windZone.windTurbulence = 2.0f * intensity;
                windZone.windPulseMagnitude = 1.0f;
                windZone.windPulseFrequency = 0.5f;
            
            // Wind particles (dust/debris)
            GameObject windObj = new GameObject("WindParticles");
            windObj.transform.SetParent(parent.transform);
            ParticleSystem windPS = windObj.AddComponent<ParticleSystem>();
            
            var main = windPS.main;
            main.startLifetime = 3.0f;
            main.startSpeed = 8.0f * intensity;
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.08f);
            main.startColor = new Color(0.7f, 0.6f, 0.5f, 0.5f);
            main.maxParticles = (int)(400 * intensity);
            
            var emission = windPS.emission;
            emission.rateOverTime = 100 * intensity;
            
            var shape = windPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(20, 2, 20);
            
            var velocityOverLifetime = windPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(5.0f * intensity, 15.0f * intensity);
            
            // Audio
            AudioSource audioSource = parent.AddComponent<AudioSource>();
            audioSource.loop = true;
            audioSource.volume = 0.4f * intensity;
            audioSource.pitch = 1.2f;
        }
        
        private static void CreateStormWeatherEffect(GameObject parent, float intensity)
        {
            // Combine rain and wind
            CreateRainWeatherEffect(parent, intensity * 1.5f);
            CreateWindWeatherEffect(parent, intensity * 1.2f);
            
            // Storm clouds
            GameObject cloudObj = new GameObject("StormClouds");
            cloudObj.transform.SetParent(parent.transform);
            ParticleSystem cloudPS = cloudObj.AddComponent<ParticleSystem>();
            
            var main = cloudPS.main;
            main.startLifetime = 15.0f;
            main.startSpeed = 1.0f;
            main.startSize = new ParticleSystem.MinMaxCurve(8.0f, 15.0f);
            main.startColor = new Color(0.3f, 0.3f, 0.4f, 0.6f * intensity);
            main.maxParticles = (int)(100 * intensity);
            
            var emission = cloudPS.emission;
            emission.rateOverTime = 10 * intensity;
            
            var shape = cloudPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(50, 10, 50);
            shape.position = new Vector3(0, 20, 0);
            
            // Lightning flashes
            Light lightning = parent.AddComponent<Light>();
            lightning.type = LightType.Directional;
            lightning.color = new Color(0.8f, 0.9f, 1.0f);
            lightning.intensity = 0;
            lightning.shadows = LightShadows.Soft;
        }
        
        private static void CreateLightningWeatherEffect(GameObject parent, float intensity)
        {
            // Lightning bolt particles
            GameObject lightningObj = new GameObject("Lightning");
            lightningObj.transform.SetParent(parent.transform);
            ParticleSystem lightningPS = lightningObj.AddComponent<ParticleSystem>();
            
            var main = lightningPS.main;
            main.startLifetime = 0.2f;
            main.startSpeed = 0;
            main.startSize = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startColor = new Color(0.8f, 0.9f, 1.0f, 1.0f);
            main.maxParticles = (int)(50 * intensity);
            
            var emission = lightningPS.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(10 * intensity))
            });
            
            var shape = lightningPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(0.1f, 20.0f, 0.1f);
            shape.position = new Vector3(0, 10, 0);
            
            // Lightning light
            Light lightningLight = parent.AddComponent<Light>();
            lightningLight.type = LightType.Point;
            lightningLight.color = new Color(0.8f, 0.9f, 1.0f);
            lightningLight.intensity = 8.0f * intensity;
            lightningLight.range = 50.0f;
            lightningLight.shadows = LightShadows.Soft;
            
            // Audio
            AudioSource audioSource = parent.AddComponent<AudioSource>();
            audioSource.volume = 0.8f * intensity;
            audioSource.pitch = 1.0f;
        }
        
        private static string ModifyWeatherEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject weatherEffect = GameObject.Find(effectName);
            if (weatherEffect == null)
                return JsonHelper.CreateErrorResponse($"Weather effect '{effectName}' not found");
            
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            
            // Modify particle systems
            ParticleSystem[] particleSystems = weatherEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                main.maxParticles = (int)(main.maxParticles * intensity);
                
                var emission = ps.emission;
                emission.rateOverTime = emission.rateOverTime.constant * intensity;
            }
            
            // Modify lights
            Light[] lights = weatherEffect.GetComponentsInChildren<Light>();
            foreach (var light in lights)
            {
                light.intensity *= intensity;
            }
            
            // Modify audio
            AudioSource[] audioSources = weatherEffect.GetComponentsInChildren<AudioSource>();
            foreach (var audio in audioSources)
            {
                audio.volume *= intensity;
            }
            
            return JsonHelper.CreateSuccessResponse($"Weather effect '{effectName}' modified successfully");
        }
        
        private static string DeleteWeatherEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject weatherEffect = GameObject.Find(effectName);
            if (weatherEffect == null)
                return JsonHelper.CreateErrorResponse($"Weather effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(weatherEffect);
            return JsonHelper.CreateSuccessResponse($"Weather effect '{effectName}' deleted successfully");
        }
        
        private static string GetWeatherEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject weatherEffect = GameObject.Find(effectName);
            if (weatherEffect == null)
                return JsonHelper.CreateErrorResponse($"Weather effect '{effectName}' not found");
            
            var properties = new
            {
                name = weatherEffect.name,
                position = weatherEffect.transform.position,
                rotation = weatherEffect.transform.rotation,
                scale = weatherEffect.transform.localScale,
                particleSystems = weatherEffect.GetComponentsInChildren<ParticleSystem>().Length,
                lights = weatherEffect.GetComponentsInChildren<Light>().Length,
                audioSources = weatherEffect.GetComponentsInChildren<AudioSource>().Length,
                windZones = weatherEffect.GetComponentsInChildren<WindZone>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("Weather effect properties retrieved", properties);
        }
        
        private static Material CreateRainMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "RainMaterial";
            material.color = new Color(0.7f, 0.8f, 1.0f, 0.8f);
            return material;
        }
        
        private static Material CreateSnowMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "SnowMaterial";
            material.color = new Color(1.0f, 1.0f, 1.0f, 0.9f);
            return material;
        }
        
        private static Material CreateFogMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "FogMaterial";
            material.color = new Color(0.8f, 0.8f, 0.9f, 0.3f);
            return material;
        }

        private static string SetupEnvironmentalEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateEnvironmentalEffect(parameters);
                    case "modify":
                        return ModifyEnvironmentalEffect(parameters);
                    case "delete":
                        return DeleteEnvironmentalEffect(parameters);
                    case "get_properties":
                        return GetEnvironmentalEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"SetupEnvironmentalEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error setting up environmental effects: {ex.Message}");
            }
        }
        
        private static string CreateEnvironmentalEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "EnvironmentalEffect";
            string environmentType = parameters["environment_type"]?.ToString() ?? "dust";
            Vector3 position = JsonHelper.ParseVector3(parameters["position"], Vector3.zero);
            Vector3 scale = JsonHelper.ParseVector3(parameters["scale"], Vector3.one);
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            
            GameObject environmentalEffect = new GameObject(effectName);
            environmentalEffect.transform.position = position;
            environmentalEffect.transform.localScale = scale;
            
            switch (environmentType.ToLower())
            {
                case "dust":
                    CreateDustEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                case "leaves":
                    CreateLeavesEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                case "ambient":
                    CreateAmbientEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                case "pollen":
                    CreatePollenEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                case "ash":
                    CreateAshEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                case "fireflies":
                    CreateFirefliesEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                case "bubbles":
                    CreateBubblesEnvironmentalEffect(environmentalEffect, intensity);
                    break;
                default:
                    CreateDustEnvironmentalEffect(environmentalEffect, intensity);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"Environmental effect '{effectName}' of type '{environmentType}' created successfully");
        }
        
        private static void CreateDustEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Dust particles
            GameObject dustObj = new GameObject("Dust");
            dustObj.transform.SetParent(parent.transform);
            ParticleSystem dustPS = dustObj.AddComponent<ParticleSystem>();
            
            var main = dustPS.main;
            main.startLifetime = 8.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.1f);
            main.startColor = new Color(0.8f, 0.7f, 0.6f, 0.3f * intensity);
            main.maxParticles = (int)(300 * intensity);
            
            var emission = dustPS.emission;
            emission.rateOverTime = 50 * intensity;
            
            var shape = dustPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(15, 5, 15);
            
            var velocityOverLifetime = dustPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var noise = dustPS.noise;
            noise.enabled = true;
            noise.strength = 0.3f;
            noise.frequency = 0.2f;
            
            // Dust material
            Material dustMaterial = CreateDustMaterial();
            var renderer = dustPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = dustMaterial;
        }
        
        private static void CreateLeavesEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Falling leaves
            GameObject leavesObj = new GameObject("Leaves");
            leavesObj.transform.SetParent(parent.transform);
            ParticleSystem leavesPS = leavesObj.AddComponent<ParticleSystem>();
            
            var main = leavesPS.main;
            main.startLifetime = 10.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.2f, 0.5f);
            main.startColor = new Color(0.8f, 0.6f, 0.2f, 0.9f);
            main.maxParticles = (int)(200 * intensity);
            
            var emission = leavesPS.emission;
            emission.rateOverTime = 20 * intensity;
            
            var shape = leavesPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(20, 0.1f, 20);
            shape.position = new Vector3(0, 10, 0);
            
            var velocityOverLifetime = leavesPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-2.0f, -1.0f);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var rotationOverLifetime = leavesPS.rotationOverLifetime;
            rotationOverLifetime.enabled = true;
            rotationOverLifetime.z = new ParticleSystem.MinMaxCurve(-180.0f, 180.0f);
            
            var noise = leavesPS.noise;
            noise.enabled = true;
            noise.strength = 0.5f;
            noise.frequency = 0.1f;
            
            // Leaves material
            Material leavesMaterial = CreateLeavesMaterial();
            var renderer = leavesPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = leavesMaterial;
        }
        
        private static void CreateAmbientEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Ambient floating particles
            GameObject ambientObj = new GameObject("AmbientParticles");
            ambientObj.transform.SetParent(parent.transform);
            ParticleSystem ambientPS = ambientObj.AddComponent<ParticleSystem>();
            
            var main = ambientPS.main;
            main.startLifetime = 15.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.1f, 0.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.01f, 0.05f);
            main.startColor = new Color(1.0f, 1.0f, 0.9f, 0.2f * intensity);
            main.maxParticles = (int)(500 * intensity);
            
            var emission = ambientPS.emission;
            emission.rateOverTime = 30 * intensity;
            
            var shape = ambientPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 10.0f;
            
            var velocityOverLifetime = ambientPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var colorOverLifetime = ambientPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.white, 0.0f), new GradientColorKey(Color.yellow, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.0f, 0.0f), new GradientAlphaKey(0.2f, 0.5f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Ambient material
            Material ambientMaterial = CreateAmbientMaterial();
            var renderer = ambientPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = ambientMaterial;
        }
        
        private static void CreatePollenEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Pollen particles
            GameObject pollenObj = new GameObject("Pollen");
            pollenObj.transform.SetParent(parent.transform);
            ParticleSystem pollenPS = pollenObj.AddComponent<ParticleSystem>();
            
            var main = pollenPS.main;
            main.startLifetime = 12.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.2f, 1.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.01f, 0.03f);
            main.startColor = new Color(1.0f, 1.0f, 0.6f, 0.4f * intensity);
            main.maxParticles = (int)(400 * intensity);
            
            var emission = pollenPS.emission;
            emission.rateOverTime = 40 * intensity;
            
            var shape = pollenPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(18, 3, 18);
            
            var velocityOverLifetime = pollenPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.8f, 0.8f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.2f, 0.8f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-0.8f, 0.8f);
            
            var noise = pollenPS.noise;
            noise.enabled = true;
            noise.strength = 0.4f;
            noise.frequency = 0.3f;
            
            // Pollen material
            Material pollenMaterial = CreatePollenMaterial();
            var renderer = pollenPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = pollenMaterial;
        }
        
        private static void CreateAshEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Ash particles
            GameObject ashObj = new GameObject("Ash");
            ashObj.transform.SetParent(parent.transform);
            ParticleSystem ashPS = ashObj.AddComponent<ParticleSystem>();
            
            var main = ashPS.main;
            main.startLifetime = 20.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.1f, 0.8f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.08f);
            main.startColor = new Color(0.4f, 0.4f, 0.4f, 0.6f * intensity);
            main.maxParticles = (int)(600 * intensity);
            
            var emission = ashPS.emission;
            emission.rateOverTime = 60 * intensity;
            
            var shape = ashPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(25, 8, 25);
            
            var velocityOverLifetime = ashPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var noise = ashPS.noise;
            noise.enabled = true;
            noise.strength = 0.2f;
            noise.frequency = 0.1f;
            
            // Ash material
            Material ashMaterial = CreateAshMaterial();
            var renderer = ashPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = ashMaterial;
        }
        
        private static void CreateFirefliesEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Fireflies particles
            GameObject firefliesObj = new GameObject("Fireflies");
            firefliesObj.transform.SetParent(parent.transform);
            ParticleSystem firefliesPS = firefliesObj.AddComponent<ParticleSystem>();
            
            var main = firefliesPS.main;
            main.startLifetime = 25.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.1f);
            main.startColor = new Color(1.0f, 1.0f, 0.6f, 0.8f);
            main.maxParticles = (int)(100 * intensity);
            
            var emission = firefliesPS.emission;
            emission.rateOverTime = 5 * intensity;
            
            var shape = firefliesPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 8.0f;
            
            var velocityOverLifetime = firefliesPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-0.5f, 1.0f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var colorOverLifetime = firefliesPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.yellow, 0.0f), new GradientColorKey(Color.green, 0.5f), new GradientColorKey(Color.yellow, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.2f, 0.0f), new GradientAlphaKey(1.0f, 0.5f), new GradientAlphaKey(0.2f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            var noise = firefliesPS.noise;
            noise.enabled = true;
            noise.strength = 1.0f;
            noise.frequency = 0.5f;
            
            // Fireflies material
            Material firefliesMaterial = CreateFirefliesMaterial();
            var renderer = firefliesPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = firefliesMaterial;
        }
        
        private static void CreateBubblesEnvironmentalEffect(GameObject parent, float intensity)
        {
            // Bubbles particles
            GameObject bubblesObj = new GameObject("Bubbles");
            bubblesObj.transform.SetParent(parent.transform);
            ParticleSystem bubblesPS = bubblesObj.AddComponent<ParticleSystem>();
            
            var main = bubblesPS.main;
            main.startLifetime = 8.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startColor = new Color(0.8f, 0.9f, 1.0f, 0.6f);
            main.maxParticles = (int)(150 * intensity);
            
            var emission = bubblesPS.emission;
            emission.rateOverTime = 15 * intensity;
            
            var shape = bubblesPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(10, 0.1f, 10);
            
            var velocityOverLifetime = bubblesPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var sizeOverLifetime = bubblesPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.8f, 1f);
            sizeCurve.AddKey(1f, 0f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Bubbles material
            Material bubblesMaterial = CreateBubblesMaterial();
            var renderer = bubblesPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = bubblesMaterial;
        }
        
        private static string ModifyEnvironmentalEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject environmentalEffect = GameObject.Find(effectName);
            if (environmentalEffect == null)
                return JsonHelper.CreateErrorResponse($"Environmental effect '{effectName}' not found");
            
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            
            // Modify particle systems
            ParticleSystem[] particleSystems = environmentalEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                main.maxParticles = (int)(main.maxParticles * intensity);
                
                var emission = ps.emission;
                emission.rateOverTime = emission.rateOverTime.constant * intensity;
            }
            
            return JsonHelper.CreateSuccessResponse($"Environmental effect '{effectName}' modified successfully");
        }
        
        private static string DeleteEnvironmentalEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject environmentalEffect = GameObject.Find(effectName);
            if (environmentalEffect == null)
                return JsonHelper.CreateErrorResponse($"Environmental effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(environmentalEffect);
            return JsonHelper.CreateSuccessResponse($"Environmental effect '{effectName}' deleted successfully");
        }
        
        private static string GetEnvironmentalEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject environmentalEffect = GameObject.Find(effectName);
            if (environmentalEffect == null)
                return JsonHelper.CreateErrorResponse($"Environmental effect '{effectName}' not found");
            
            var properties = new
            {
                name = environmentalEffect.name,
                position = environmentalEffect.transform.position,
                rotation = environmentalEffect.transform.rotation,
                scale = environmentalEffect.transform.localScale,
                particleSystems = environmentalEffect.GetComponentsInChildren<ParticleSystem>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("Environmental effect properties retrieved", properties);
        }
        
        private static Material CreateDustMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "DustMaterial";
            material.color = new Color(0.8f, 0.7f, 0.6f, 0.3f);
            return material;
        }
        
        private static Material CreateLeavesMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "LeavesMaterial";
            material.color = new Color(0.8f, 0.6f, 0.2f, 0.9f);
            return material;
        }
        
        private static Material CreateAmbientMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "AmbientMaterial";
            material.color = new Color(1.0f, 1.0f, 0.9f, 0.2f);
            return material;
        }
        
        private static Material CreatePollenMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "PollenMaterial";
            material.color = new Color(1.0f, 1.0f, 0.6f, 0.4f);
            return material;
        }
        
        private static Material CreateAshMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "AshMaterial";
            material.color = new Color(0.4f, 0.4f, 0.4f, 0.6f);
            return material;
        }
        
        private static Material CreateFirefliesMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "FirefliesMaterial";
            material.color = new Color(1.0f, 1.0f, 0.6f, 0.8f);
            return material;
        }
        
        private static Material CreateBubblesMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "BubblesMaterial";
            material.color = new Color(0.8f, 0.9f, 1.0f, 0.6f);
            return material;
        }

        private static string CreateUiEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateUiEffect(parameters);
                    case "modify":
                        return ModifyUiEffect(parameters);
                    case "delete":
                        return DeleteUiEffect(parameters);
                    case "get_properties":
                        return GetUiEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateUiEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating UI effects: {ex.Message}");
            }
        }
        
        private static string CreateUiEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "UiEffect";
            string uiType = parameters["ui_type"]?.ToString() ?? "button_click";
            Vector3 position = JsonHelper.ParseVector3(parameters["position"], Vector3.zero);
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            
            GameObject uiEffect = new GameObject(effectName);
            uiEffect.transform.position = position;
            
            switch (uiType.ToLower())
            {
                case "button_click":
                    CreateButtonClickEffect(uiEffect, intensity);
                    break;
                case "hover":
                    CreateHoverEffect(uiEffect, intensity);
                    break;
                case "transition":
                    CreateTransitionEffect(uiEffect, intensity);
                    break;
                case "notification":
                    CreateNotificationEffect(uiEffect, intensity);
                    break;
                case "loading":
                    CreateLoadingEffect(uiEffect, intensity);
                    break;
                case "success":
                    CreateSuccessEffect(uiEffect, intensity);
                    break;
                case "error":
                    CreateErrorEffect(uiEffect, intensity);
                    break;
                case "sparkle":
                    CreateSparkleEffect(uiEffect, intensity);
                    break;
                default:
                    CreateButtonClickEffect(uiEffect, intensity);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"UI effect '{effectName}' of type '{uiType}' created successfully");
        }
        
        private static void CreateButtonClickEffect(GameObject parent, float intensity)
        {
            // Button click burst effect
            GameObject clickObj = new GameObject("ButtonClick");
            clickObj.transform.SetParent(parent.transform);
            ParticleSystem clickPS = clickObj.AddComponent<ParticleSystem>();
            
            var main = clickPS.main;
            main.startLifetime = 0.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.15f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, 0.8f);
            main.maxParticles = (int)(20 * intensity);
            
            var emission = clickPS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(15 * intensity))
            });
            
            var shape = clickPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.1f;
            
            var velocityOverLifetime = clickPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(3.0f, 8.0f);
            
            var sizeOverLifetime = clickPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 1f);
            sizeCurve.AddKey(1f, 0f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Click material
            Material clickMaterial = CreateUiClickMaterial();
            var renderer = clickPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = clickMaterial;
        }
        
        private static void CreateHoverEffect(GameObject parent, float intensity)
        {
            // Hover glow effect
            GameObject hoverObj = new GameObject("Hover");
            hoverObj.transform.SetParent(parent.transform);
            ParticleSystem hoverPS = hoverObj.AddComponent<ParticleSystem>();
            
            var main = hoverPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startColor = new Color(0.5f, 0.8f, 1.0f, 0.4f * intensity);
            main.maxParticles = (int)(30 * intensity);
            main.loop = true;
            
            var emission = hoverPS.emission;
            emission.rateOverTime = 10 * intensity;
            
            var shape = hoverPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(1.0f, 1.0f, 0.1f);
            
            var velocityOverLifetime = hoverPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.5f, 1.0f);
            
            var colorOverLifetime = hoverPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.cyan, 0.0f), new GradientColorKey(Color.blue, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.0f, 0.0f), new GradientAlphaKey(0.4f, 0.5f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Hover material
            Material hoverMaterial = CreateUiHoverMaterial();
            var renderer = hoverPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = hoverMaterial;
        }
        
        private static void CreateTransitionEffect(GameObject parent, float intensity)
        {
            // Transition sweep effect
            GameObject transitionObj = new GameObject("Transition");
            transitionObj.transform.SetParent(parent.transform);
            ParticleSystem transitionPS = transitionObj.AddComponent<ParticleSystem>();
            
            var main = transitionPS.main;
            main.startLifetime = 1.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(3.0f, 6.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.08f);
            main.startColor = new Color(1.0f, 0.8f, 0.2f, 0.7f);
            main.maxParticles = (int)(50 * intensity);
            
            var emission = transitionPS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(40 * intensity))
            });
            
            var shape = transitionPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.SingleSidedEdge;
            shape.radius = 2.0f;
            
            var velocityOverLifetime = transitionPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(5.0f, 10.0f);
            
            var sizeOverLifetime = transitionPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.3f, 1f);
            sizeCurve.AddKey(1f, 0f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Transition material
            Material transitionMaterial = CreateUiTransitionMaterial();
            var renderer = transitionPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = transitionMaterial;
        }
        
        private static void CreateNotificationEffect(GameObject parent, float intensity)
        {
            // Notification pulse effect
            GameObject notificationObj = new GameObject("Notification");
            notificationObj.transform.SetParent(parent.transform);
            ParticleSystem notificationPS = notificationObj.AddComponent<ParticleSystem>();
            
            var main = notificationPS.main;
            main.startLifetime = 1.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.2f);
            main.startColor = new Color(1.0f, 0.5f, 0.0f, 0.8f);
            main.maxParticles = (int)(25 * intensity);
            
            var emission = notificationPS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(20 * intensity)),
                new ParticleSystem.Burst(0.3f, (short)(15 * intensity))
            });
            
            var shape = notificationPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.2f;
            
            var velocityOverLifetime = notificationPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(2.0f, 4.0f);
            
            var colorOverLifetime = notificationPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.red, 0.0f), new GradientColorKey(Color.yellow, 0.5f), new GradientColorKey(Color.orange, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.8f, 0.0f), new GradientAlphaKey(1.0f, 0.3f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Notification material
            Material notificationMaterial = CreateUiNotificationMaterial();
            var renderer = notificationPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = notificationMaterial;
        }
        
        private static void CreateLoadingEffect(GameObject parent, float intensity)
        {
            // Loading spinner effect
            GameObject loadingObj = new GameObject("Loading");
            loadingObj.transform.SetParent(parent.transform);
            ParticleSystem loadingPS = loadingObj.AddComponent<ParticleSystem>();
            
            var main = loadingPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 4.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.1f);
            main.startColor = new Color(0.2f, 0.8f, 1.0f, 0.7f);
            main.maxParticles = (int)(40 * intensity);
            main.loop = true;
            
            var emission = loadingPS.emission;
            emission.rateOverTime = 20 * intensity;
            
            var shape = loadingPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.5f;
            
            var velocityOverLifetime = loadingPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.orbitalX = new ParticleSystem.MinMaxCurve(90.0f, 180.0f);
            
            var colorOverLifetime = loadingPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.cyan, 0.0f), new GradientColorKey(Color.blue, 0.5f), new GradientColorKey(Color.cyan, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.3f, 0.0f), new GradientAlphaKey(1.0f, 0.5f), new GradientAlphaKey(0.3f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Loading material
            Material loadingMaterial = CreateUiLoadingMaterial();
            var renderer = loadingPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = loadingMaterial;
        }
        
        private static void CreateSuccessEffect(GameObject parent, float intensity)
        {
            // Success celebration effect
            GameObject successObj = new GameObject("Success");
            successObj.transform.SetParent(parent.transform);
            ParticleSystem successPS = successObj.AddComponent<ParticleSystem>();
            
            var main = successPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(3.0f, 8.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.08f, 0.15f);
            main.startColor = new Color(0.2f, 1.0f, 0.2f, 0.9f);
            main.maxParticles = (int)(60 * intensity);
            
            var emission = successPS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(50 * intensity))
            });
            
            var shape = successPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Cone;
            shape.angle = 45.0f;
            
            var velocityOverLifetime = successPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(5.0f, 10.0f);
            
            var forceOverLifetime = successPS.forceOverLifetime;
            forceOverLifetime.enabled = true;
            forceOverLifetime.y = new ParticleSystem.MinMaxCurve(-9.8f);
            
            var colorOverLifetime = successPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.green, 0.0f), new GradientColorKey(Color.yellow, 0.5f), new GradientColorKey(Color.green, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.9f, 0.0f), new GradientAlphaKey(1.0f, 0.3f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Success material
            Material successMaterial = CreateUiSuccessMaterial();
            var renderer = successPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = successMaterial;
        }
        
        private static void CreateErrorEffect(GameObject parent, float intensity)
        {
            // Error shake effect
            GameObject errorObj = new GameObject("Error");
            errorObj.transform.SetParent(parent.transform);
            ParticleSystem errorPS = errorObj.AddComponent<ParticleSystem>();
            
            var main = errorPS.main;
            main.startLifetime = 1.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.12f);
            main.startColor = new Color(1.0f, 0.2f, 0.2f, 0.8f);
            main.maxParticles = (int)(30 * intensity);
            
            var emission = errorPS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(25 * intensity)),
                new ParticleSystem.Burst(0.2f, (short)(15 * intensity))
            });
            
            var shape = errorPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(0.5f, 0.5f, 0.1f);
            
            var velocityOverLifetime = errorPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-3.0f, 3.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-2.0f, 2.0f);
            
            var noise = errorPS.noise;
            noise.enabled = true;
            noise.strength = 2.0f;
            noise.frequency = 5.0f;
            
            // Error material
            Material errorMaterial = CreateUiErrorMaterial();
            var renderer = errorPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = errorMaterial;
        }
        
        private static void CreateSparkleEffect(GameObject parent, float intensity)
        {
            // Sparkle twinkle effect
            GameObject sparkleObj = new GameObject("Sparkle");
            sparkleObj.transform.SetParent(parent.transform);
            ParticleSystem sparklePS = sparkleObj.AddComponent<ParticleSystem>();
            
            var main = sparklePS.main;
            main.startLifetime = 3.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.03f, 0.08f);
            main.startColor = new Color(1.0f, 1.0f, 0.8f, 0.9f);
            main.maxParticles = (int)(80 * intensity);
            main.loop = true;
            
            var emission = sparklePS.emission;
            emission.rateOverTime = 25 * intensity;
            
            var shape = sparklePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(2.0f, 2.0f, 0.1f);
            
            var velocityOverLifetime = sparklePS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var colorOverLifetime = sparklePS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.white, 0.0f), new GradientColorKey(Color.yellow, 0.5f), new GradientColorKey(Color.white, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.0f, 0.0f), new GradientAlphaKey(1.0f, 0.5f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            var sizeOverLifetime = sparklePS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 0f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Sparkle material
            Material sparkleMaterial = CreateUiSparkleMaterial();
            var renderer = sparklePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = sparkleMaterial;
        }
        
        private static string ModifyUiEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject uiEffect = GameObject.Find(effectName);
            if (uiEffect == null)
                return JsonHelper.CreateErrorResponse($"UI effect '{effectName}' not found");
            
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            
            // Modify particle systems
            ParticleSystem[] particleSystems = uiEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                main.maxParticles = (int)(main.maxParticles * intensity);
                
                var emission = ps.emission;
                if (emission.enabled)
                {
                    emission.rateOverTime = emission.rateOverTime.constant * intensity;
                }
            }
            
            return JsonHelper.CreateSuccessResponse($"UI effect '{effectName}' modified successfully");
        }
        
        private static string DeleteUiEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject uiEffect = GameObject.Find(effectName);
            if (uiEffect == null)
                return JsonHelper.CreateErrorResponse($"UI effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(uiEffect);
            return JsonHelper.CreateSuccessResponse($"UI effect '{effectName}' deleted successfully");
        }
        
        private static string GetUiEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject uiEffect = GameObject.Find(effectName);
            if (uiEffect == null)
                return JsonHelper.CreateErrorResponse($"UI effect '{effectName}' not found");
            
            var properties = new
            {
                name = uiEffect.name,
                position = uiEffect.transform.position,
                rotation = uiEffect.transform.rotation,
                scale = uiEffect.transform.localScale,
                particleSystems = uiEffect.GetComponentsInChildren<ParticleSystem>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("UI effect properties retrieved", properties);
        }
        
        private static Material CreateUiClickMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiClickMaterial";
            material.color = new Color(1.0f, 1.0f, 1.0f, 0.8f);
            return material;
        }
        
        private static Material CreateUiHoverMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiHoverMaterial";
            material.color = new Color(0.5f, 0.8f, 1.0f, 0.4f);
            return material;
        }
        
        private static Material CreateUiTransitionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiTransitionMaterial";
            material.color = new Color(1.0f, 0.8f, 0.2f, 0.7f);
            return material;
        }
        
        private static Material CreateUiNotificationMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiNotificationMaterial";
            material.color = new Color(1.0f, 0.5f, 0.0f, 0.8f);
            return material;
        }
        
        private static Material CreateUiLoadingMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiLoadingMaterial";
            material.color = new Color(0.2f, 0.8f, 1.0f, 0.7f);
            return material;
        }
        
        private static Material CreateUiSuccessMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiSuccessMaterial";
            material.color = new Color(0.2f, 1.0f, 0.2f, 0.9f);
            return material;
        }
        
        private static Material CreateUiErrorMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiErrorMaterial";
            material.color = new Color(1.0f, 0.2f, 0.2f, 0.8f);
            return material;
        }
        
        private static Material CreateUiSparkleMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "UiSparkleMaterial";
            material.color = new Color(1.0f, 1.0f, 0.8f, 0.9f);
            return material;
        }

        private static string SetupScreenEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateScreenEffect(parameters);
                    case "modify":
                        return ModifyScreenEffect(parameters);
                    case "delete":
                        return DeleteScreenEffect(parameters);
                    case "get_properties":
                        return GetScreenEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"SetupScreenEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error setting up screen effects: {ex.Message}");
            }
        }
        
        private static string CreateScreenEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "ScreenEffect";
            string screenType = parameters["screen_type"]?.ToString() ?? "shake";
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            float duration = (float)(parameters["duration"] ?? 1.0f);
            
            GameObject screenEffect = new GameObject(effectName);
            
            switch (screenType.ToLower())
            {
                case "shake":
                    CreateScreenShakeEffect(screenEffect, intensity, duration);
                    break;
                case "flash":
                    CreateScreenFlashEffect(screenEffect, intensity, duration);
                    break;
                case "fade":
                    CreateScreenFadeEffect(screenEffect, intensity, duration);
                    break;
                case "distortion":
                    CreateScreenDistortionEffect(screenEffect, intensity, duration);
                    break;
                case "blur":
                    CreateScreenBlurEffect(screenEffect, intensity, duration);
                    break;
                case "vignette":
                    CreateScreenVignetteEffect(screenEffect, intensity, duration);
                    break;
                case "chromatic":
                    CreateChromaticAberrationEffect(screenEffect, intensity, duration);
                    break;
                case "glitch":
                    CreateScreenGlitchEffect(screenEffect, intensity, duration);
                    break;
                default:
                    CreateScreenShakeEffect(screenEffect, intensity, duration);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"Screen effect '{effectName}' of type '{screenType}' created successfully");
        }
        
        private static void CreateScreenShakeEffect(GameObject parent, float intensity, float duration)
        {
            // Screen shake using particle system for visual feedback
            GameObject shakeObj = new GameObject("ScreenShake");
            shakeObj.transform.SetParent(parent.transform);
            ParticleSystem shakePS = shakeObj.AddComponent<ParticleSystem>();
            
            var main = shakePS.main;
            main.startLifetime = duration;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.1f, 0.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.01f, 0.03f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, 0.1f * intensity);
            main.maxParticles = (int)(100 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = shakePS.emission;
            emission.rateOverTime = 50 * intensity;
            
            var shape = shakePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(20.0f, 15.0f, 1.0f);
            
            var velocityOverLifetime = shakePS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-intensity, intensity);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-intensity, intensity);
            
            var noise = shakePS.noise;
            noise.enabled = true;
            noise.strength = intensity * 2.0f;
            noise.frequency = 10.0f;
            
            // Shake material
            Material shakeMaterial = CreateScreenShakeMaterial();
            var renderer = shakePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = shakeMaterial;
        }
        
        private static void CreateScreenFlashEffect(GameObject parent, float intensity, float duration)
        {
            // Screen flash using bright particles
            GameObject flashObj = new GameObject("ScreenFlash");
            flashObj.transform.SetParent(parent.transform);
            ParticleSystem flashPS = flashObj.AddComponent<ParticleSystem>();
            
            var main = flashPS.main;
            main.startLifetime = duration;
            main.startSpeed = 0.0f;
            main.startSize = new ParticleSystem.MinMaxCurve(50.0f, 100.0f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, intensity);
            main.maxParticles = 1;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = flashPS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 1)
            });
            
            var shape = flashPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(50.0f, 50.0f, 1.0f);
            
            var colorOverLifetime = flashPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.white, 0.0f), new GradientColorKey(Color.white, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Flash material
            Material flashMaterial = CreateScreenFlashMaterial();
            var renderer = flashPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = flashMaterial;
        }
        
        private static void CreateScreenFadeEffect(GameObject parent, float intensity, float duration)
        {
            // Screen fade using overlay particles
            GameObject fadeObj = new GameObject("ScreenFade");
            fadeObj.transform.SetParent(parent.transform);
            ParticleSystem fadePS = fadeObj.AddComponent<ParticleSystem>();
            
            var main = fadePS.main;
            main.startLifetime = duration;
            main.startSpeed = 0.0f;
            main.startSize = new ParticleSystem.MinMaxCurve(100.0f, 150.0f);
            main.startColor = new Color(0.0f, 0.0f, 0.0f, 0.0f);
            main.maxParticles = 1;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = fadePS.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 1)
            });
            
            var shape = fadePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(100.0f, 100.0f, 1.0f);
            
            var colorOverLifetime = fadePS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.black, 0.0f), new GradientColorKey(Color.black, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.0f, 0.0f), new GradientAlphaKey(intensity, 0.5f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Fade material
            Material fadeMaterial = CreateScreenFadeMaterial();
            var renderer = fadePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = fadeMaterial;
        }
        
        private static void CreateScreenDistortionEffect(GameObject parent, float intensity, float duration)
        {
            // Screen distortion using animated particles
            GameObject distortionObj = new GameObject("ScreenDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem distortionPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = distortionPS.main;
            main.startLifetime = duration;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startColor = new Color(0.5f, 0.8f, 1.0f, 0.3f * intensity);
            main.maxParticles = (int)(50 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = distortionPS.emission;
            emission.rateOverTime = 20 * intensity;
            
            var shape = distortionPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(30.0f, 20.0f, 1.0f);
            
            var velocityOverLifetime = distortionPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-2.0f, 2.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var textureSheetAnimation = distortionPS.textureSheetAnimation;
            textureSheetAnimation.enabled = true;
            textureSheetAnimation.numTilesX = 4;
            textureSheetAnimation.numTilesY = 4;
            textureSheetAnimation.animation = ParticleSystemAnimationType.WholeSheet;
            textureSheetAnimation.frameOverTime = new ParticleSystem.MinMaxCurve(1.0f);
            
            // Distortion material
            Material distortionMaterial = CreateScreenDistortionMaterial();
            var renderer = distortionPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = distortionMaterial;
        }
        
        private static void CreateScreenBlurEffect(GameObject parent, float intensity, float duration)
        {
            // Screen blur using soft particles
            GameObject blurObj = new GameObject("ScreenBlur");
            blurObj.transform.SetParent(parent.transform);
            ParticleSystem blurPS = blurObj.AddComponent<ParticleSystem>();
            
            var main = blurPS.main;
            main.startLifetime = duration;
            main.startSpeed = 0.0f;
            main.startSize = new ParticleSystem.MinMaxCurve(10.0f, 20.0f);
            main.startColor = new Color(0.8f, 0.8f, 0.8f, 0.2f * intensity);
            main.maxParticles = (int)(20 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = blurPS.emission;
            emission.rateOverTime = 10 * intensity;
            
            var shape = blurPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(40.0f, 30.0f, 1.0f);
            
            var velocityOverLifetime = blurPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var sizeOverLifetime = blurPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 0.5f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            // Blur material
            Material blurMaterial = CreateScreenBlurMaterial();
            var renderer = blurPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = blurMaterial;
        }
        
        private static void CreateScreenVignetteEffect(GameObject parent, float intensity, float duration)
        {
            // Screen vignette using edge particles
            GameObject vignetteObj = new GameObject("ScreenVignette");
            vignetteObj.transform.SetParent(parent.transform);
            ParticleSystem vignettePS = vignetteObj.AddComponent<ParticleSystem>();
            
            var main = vignettePS.main;
            main.startLifetime = duration;
            main.startSpeed = 0.0f;
            main.startSize = new ParticleSystem.MinMaxCurve(5.0f, 15.0f);
            main.startColor = new Color(0.0f, 0.0f, 0.0f, intensity);
            main.maxParticles = (int)(40 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = vignettePS.emission;
            emission.rateOverTime = 20 * intensity;
            
            var shape = vignettePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 25.0f;
            shape.radiusThickness = 0.8f;
            
            var colorOverLifetime = vignettePS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(Color.black, 0.0f), new GradientColorKey(Color.black, 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.0f, 0.0f), new GradientAlphaKey(intensity, 0.5f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Vignette material
            Material vignetteMaterial = CreateScreenVignetteMaterial();
            var renderer = vignettePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = vignetteMaterial;
        }
        
        private static void CreateChromaticAberrationEffect(GameObject parent, float intensity, float duration)
        {
            // Chromatic aberration using colored particles
            GameObject chromaticObj = new GameObject("ChromaticAberration");
            chromaticObj.transform.SetParent(parent.transform);
            
            // Red channel
            GameObject redObj = new GameObject("RedChannel");
            redObj.transform.SetParent(chromaticObj.transform);
            ParticleSystem redPS = redObj.AddComponent<ParticleSystem>();
            SetupChromaticChannel(redPS, Color.red, intensity, duration, new Vector3(-0.1f, 0, 0));
            
            // Green channel
            GameObject greenObj = new GameObject("GreenChannel");
            greenObj.transform.SetParent(chromaticObj.transform);
            ParticleSystem greenPS = greenObj.AddComponent<ParticleSystem>();
            SetupChromaticChannel(greenPS, Color.green, intensity, duration, Vector3.zero);
            
            // Blue channel
            GameObject blueObj = new GameObject("BlueChannel");
            blueObj.transform.SetParent(chromaticObj.transform);
            ParticleSystem bluePS = blueObj.AddComponent<ParticleSystem>();
            SetupChromaticChannel(bluePS, Color.blue, intensity, duration, new Vector3(0.1f, 0, 0));
        }
        
        private static void SetupChromaticChannel(ParticleSystem ps, Color channelColor, float intensity, float duration, Vector3 offset)
        {
            var main = ps.main;
            main.startLifetime = duration;
            main.startSpeed = 0.0f;
            main.startSize = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            main.startColor = new Color(channelColor.r, channelColor.g, channelColor.b, 0.3f * intensity);
            main.maxParticles = (int)(30 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = ps.emission;
            emission.rateOverTime = 15 * intensity;
            
            var shape = ps.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(35.0f, 25.0f, 1.0f);
            shape.position = offset * intensity;
            
            // Chromatic material
            Material chromaticMaterial = CreateChromaticMaterial(channelColor);
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            renderer.material = chromaticMaterial;
        }
        
        private static void CreateScreenGlitchEffect(GameObject parent, float intensity, float duration)
        {
            // Screen glitch using erratic particles
            GameObject glitchObj = new GameObject("ScreenGlitch");
            glitchObj.transform.SetParent(parent.transform);
            ParticleSystem glitchPS = glitchObj.AddComponent<ParticleSystem>();
            
            var main = glitchPS.main;
            main.startLifetime = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startSpeed = new ParticleSystem.MinMaxCurve(5.0f, 15.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 2.0f);
            main.startColor = new Color(1.0f, 0.0f, 1.0f, 0.8f * intensity);
            main.maxParticles = (int)(100 * intensity);
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            main.loop = true;
            
            var emission = glitchPS.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, (short)(50 * intensity)),
                new ParticleSystem.Burst(0.2f, (short)(30 * intensity)),
                new ParticleSystem.Burst(0.5f, (short)(40 * intensity))
            });
            
            var shape = glitchPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(40.0f, 30.0f, 1.0f);
            
            var velocityOverLifetime = glitchPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-10.0f, 10.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-5.0f, 5.0f);
            
            var noise = glitchPS.noise;
            noise.enabled = true;
            noise.strength = intensity * 5.0f;
            noise.frequency = 20.0f;
            
            // Glitch material
            Material glitchMaterial = CreateScreenGlitchMaterial();
            var renderer = glitchPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = glitchMaterial;
        }
        
        private static string ModifyScreenEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject screenEffect = GameObject.Find(effectName);
            if (screenEffect == null)
                return JsonHelper.CreateErrorResponse($"Screen effect '{effectName}' not found");
            
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            float duration = (float)(parameters["duration"] ?? 1.0f);
            
            // Modify particle systems
            ParticleSystem[] particleSystems = screenEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                main.startLifetime = duration;
                
                var startColor = main.startColor;
                if (startColor.mode == ParticleSystemGradientMode.Color)
                {
                    Color color = startColor.color;
                    color.a *= intensity;
                    main.startColor = color;
                }
            }
            
            return JsonHelper.CreateSuccessResponse($"Screen effect '{effectName}' modified successfully");
        }
        
        private static string DeleteScreenEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject screenEffect = GameObject.Find(effectName);
            if (screenEffect == null)
                return JsonHelper.CreateErrorResponse($"Screen effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(screenEffect);
            return JsonHelper.CreateSuccessResponse($"Screen effect '{effectName}' deleted successfully");
        }
        
        private static string GetScreenEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject screenEffect = GameObject.Find(effectName);
            if (screenEffect == null)
                return JsonHelper.CreateErrorResponse($"Screen effect '{effectName}' not found");
            
            var properties = new
            {
                name = screenEffect.name,
                position = screenEffect.transform.position,
                rotation = screenEffect.transform.rotation,
                scale = screenEffect.transform.localScale,
                particleSystems = screenEffect.GetComponentsInChildren<ParticleSystem>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("Screen effect properties retrieved", properties);
        }
        
        private static Material CreateScreenShakeMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenShakeMaterial";
            material.color = new Color(1.0f, 1.0f, 1.0f, 0.1f);
            return material;
        }
        
        private static Material CreateScreenFlashMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenFlashMaterial";
            material.color = new Color(1.0f, 1.0f, 1.0f, 1.0f);
            return material;
        }
        
        private static Material CreateScreenFadeMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenFadeMaterial";
            material.color = new Color(0.0f, 0.0f, 0.0f, 1.0f);
            return material;
        }
        
        private static Material CreateScreenDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenDistortionMaterial";
            material.color = new Color(0.5f, 0.8f, 1.0f, 0.3f);
            return material;
        }
        
        private static Material CreateScreenBlurMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenBlurMaterial";
            material.color = new Color(0.8f, 0.8f, 0.8f, 0.2f);
            return material;
        }
        
        private static Material CreateScreenVignetteMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenVignetteMaterial";
            material.color = new Color(0.0f, 0.0f, 0.0f, 1.0f);
            return material;
        }
        
        private static Material CreateChromaticMaterial(Color channelColor)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = $"ChromaticMaterial_{channelColor.ToString()}";
            material.color = channelColor;
            return material;
        }
        
        private static Material CreateScreenGlitchMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScreenGlitchMaterial";
            material.color = new Color(1.0f, 0.0f, 1.0f, 0.8f);
            return material;
        }

        private static string CreateTrailEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateTrailEffect(parameters);
                    case "modify":
                        return ModifyTrailEffect(parameters);
                    case "delete":
                        return DeleteTrailEffect(parameters);
                    case "get_properties":
                        return GetTrailEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateTrailEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating trail effects: {ex.Message}");
            }
        }
        
        private static string CreateTrailEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "TrailEffect";
            string trailType = parameters["trail_type"]?.ToString() ?? "weapon";
            float width = (float)(parameters["width"] ?? 1.0f);
            float time = (float)(parameters["time"] ?? 1.0f);
            
            GameObject trailEffect = new GameObject(effectName);
            
            switch (trailType.ToLower())
            {
                case "weapon":
                    CreateWeaponTrail(trailEffect, width, time);
                    break;
                case "movement":
                    CreateMovementTrail(trailEffect, width, time);
                    break;
                case "magic":
                    CreateMagicTrail(trailEffect, width, time);
                    break;
                case "projectile":
                    CreateProjectileTrail(trailEffect, width, time);
                    break;
                case "energy":
                    CreateEnergyTrail(trailEffect, width, time);
                    break;
                case "smoke":
                    CreateSmokeTrail(trailEffect, width, time);
                    break;
                case "fire":
                    CreateFireTrail(trailEffect, width, time);
                    break;
                case "lightning":
                    CreateLightningTrail(trailEffect, width, time);
                    break;
                default:
                    CreateWeaponTrail(trailEffect, width, time);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"Trail effect '{effectName}' of type '{trailType}' created successfully");
        }
        
        private static void CreateWeaponTrail(GameObject parent, float width, float time)
        {
            // Weapon trail using TrailRenderer
            GameObject trailObj = new GameObject("WeaponTrail");
            trailObj.transform.SetParent(parent.transform);
            TrailRenderer trail = trailObj.AddComponent<TrailRenderer>();
            
            trail.time = time;
            trail.startWidth = width;
            trail.endWidth = 0.1f;
            trail.numCornerVertices = 5;
            trail.numCapVertices = 5;
            // Fixed: useWorldSpace deprecated in Unity 6.2 TrailRenderer
            // trail.useWorldSpace = true;
            trail.generateLightingData = true;
            
            // Weapon trail material
            Material weaponMaterial = CreateWeaponTrailMaterial();
            trail.material = weaponMaterial;
            
            // Add particle system for sparks
            ParticleSystem sparks = trailObj.AddComponent<ParticleSystem>();
            var main = sparks.main;
            main.startLifetime = 0.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.15f);
            main.startColor = new Color(1.0f, 0.8f, 0.2f, 1.0f);
            main.maxParticles = 50;
            
            var emission = sparks.emission;
            emission.rateOverTime = 20;
            
            var shape = sparks.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.1f;
            
            Material sparkMaterial = CreateSparkMaterial("WeaponTrailSparkMaterial");
            var sparkRenderer = sparks.GetComponent<ParticleSystemRenderer>();
            sparkRenderer.material = sparkMaterial;
        }
        
        private static void CreateMovementTrail(GameObject parent, float width, float time)
        {
            // Movement trail using particle system
            GameObject trailObj = new GameObject("MovementTrail");
            trailObj.transform.SetParent(parent.transform);
            ParticleSystem trail = trailObj.AddComponent<ParticleSystem>();
            
            var main = trail.main;
            main.startLifetime = time;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(width * 0.5f, width);
            main.startColor = new Color(0.2f, 0.6f, 1.0f, 0.8f);
            main.maxParticles = 100;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = trail.emission;
            emission.rateOverTime = 30;
            
            var shape = trail.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.2f;
            
            var velocityOverLifetime = trail.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-1.0f, -3.0f);
            
            var colorOverLifetime = trail.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.2f, 0.6f, 1.0f), 0.0f), new GradientColorKey(new Color(0.1f, 0.3f, 0.8f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.8f, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material movementMaterial = CreateMovementTrailMaterial();
            var renderer = trail.GetComponent<ParticleSystemRenderer>();
            renderer.material = movementMaterial;
        }
        
        private static void CreateMagicTrail(GameObject parent, float width, float time)
        {
            // Magic trail with mystical effects
            GameObject trailObj = new GameObject("MagicTrail");
            trailObj.transform.SetParent(parent.transform);
            
            // Main trail renderer
            TrailRenderer trail = trailObj.AddComponent<TrailRenderer>();
            trail.time = time;
            trail.startWidth = width;
            trail.endWidth = 0.05f;
            trail.numCornerVertices = 8;
            trail.numCapVertices = 8;
            // Fixed: useWorldSpace deprecated in Unity 6.2 TrailRenderer
            // trail.useWorldSpace = true;
            
            Material magicMaterial = CreateMagicTrailMaterial();
            trail.material = magicMaterial;
            
            // Magic particles
            ParticleSystem magicPS = trailObj.AddComponent<ParticleSystem>();
            var main = magicPS.main;
            main.startLifetime = time * 0.8f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.1f, 0.3f);
            main.startColor = new Color(0.8f, 0.2f, 1.0f, 0.9f);
            main.maxParticles = 80;
            
            var emission = magicPS.emission;
            emission.rateOverTime = 25;
            
            var shape = magicPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = width * 0.3f;
            
            var textureSheetAnimation = magicPS.textureSheetAnimation;
            textureSheetAnimation.enabled = true;
            textureSheetAnimation.numTilesX = 4;
            textureSheetAnimation.numTilesY = 4;
            textureSheetAnimation.animation = ParticleSystemAnimationType.WholeSheet;
            textureSheetAnimation.frameOverTime = new ParticleSystem.MinMaxCurve(1.0f);
            
            Material magicParticleMaterial = CreateMagicParticleMaterial();
            var magicRenderer = magicPS.GetComponent<ParticleSystemRenderer>();
            magicRenderer.material = magicParticleMaterial;
        }
        
        private static void CreateProjectileTrail(GameObject parent, float width, float time)
        {
            // Projectile trail with speed lines
            GameObject trailObj = new GameObject("ProjectileTrail");
            trailObj.transform.SetParent(parent.transform);
            
            TrailRenderer trail = trailObj.AddComponent<TrailRenderer>();
            trail.time = time;
            trail.startWidth = width;
            trail.endWidth = 0.02f;
            trail.numCornerVertices = 3;
            trail.numCapVertices = 3;
            // Fixed: useWorldSpace deprecated in Unity 6.2 TrailRenderer
            // trail.useWorldSpace = true;
            
            Material projectileMaterial = CreateProjectileTrailMaterial();
            trail.material = projectileMaterial;
            
            // Speed lines particle system
            ParticleSystem speedLines = trailObj.AddComponent<ParticleSystem>();
            var main = speedLines.main;
            main.startLifetime = 0.3f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(8.0f, 15.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.08f);
            main.startColor = new Color(1.0f, 1.0f, 0.8f, 0.7f);
            main.maxParticles = 30;
            
            var emission = speedLines.emission;
            emission.rateOverTime = 40;
            
            var shape = speedLines.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Cone;
            shape.angle = 15.0f;
            shape.radius = 0.05f;
            
            Material speedLineMaterial = CreateSpeedLineMaterial();
            var speedRenderer = speedLines.GetComponent<ParticleSystemRenderer>();
            speedRenderer.material = speedLineMaterial;
        }
        
        private static void CreateEnergyTrail(GameObject parent, float width, float time)
        {
            // Energy trail with electric effects
            GameObject trailObj = new GameObject("EnergyTrail");
            trailObj.transform.SetParent(parent.transform);
            
            TrailRenderer trail = trailObj.AddComponent<TrailRenderer>();
            trail.time = time;
            trail.startWidth = width;
            trail.endWidth = 0.1f;
            trail.numCornerVertices = 6;
            trail.numCapVertices = 6;
            // Fixed: useWorldSpace deprecated in Unity 6.2 TrailRenderer
            // trail.useWorldSpace = true;
            
            Material energyMaterial = CreateEnergyTrailMaterial();
            trail.material = energyMaterial;
            
            // Electric arcs
            ParticleSystem arcs = trailObj.AddComponent<ParticleSystem>();
            var main = arcs.main;
            main.startLifetime = 0.2f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 4.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.05f, 0.2f);
            main.startColor = new Color(0.2f, 0.8f, 1.0f, 1.0f);
            main.maxParticles = 60;
            
            var emission = arcs.emission;
            emission.rateOverTime = 35;
            
            var noise = arcs.noise;
            noise.enabled = true;
            noise.strength = 2.0f;
            noise.frequency = 5.0f;
            
            Material arcMaterial = CreateElectricArcMaterial();
            var arcRenderer = arcs.GetComponent<ParticleSystemRenderer>();
            arcRenderer.material = arcMaterial;
        }
        
        private static void CreateSmokeTrail(GameObject parent, float width, float time)
        {
            // Smoke trail using particle system
            GameObject trailObj = new GameObject("SmokeTrail");
            trailObj.transform.SetParent(parent.transform);
            ParticleSystem smoke = trailObj.AddComponent<ParticleSystem>();
            
            var main = smoke.main;
            main.startLifetime = time * 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(width * 0.3f, width * 1.5f);
            main.startColor = new Color(0.3f, 0.3f, 0.3f, 0.6f);
            main.maxParticles = 150;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = smoke.emission;
            emission.rateOverTime = 20;
            
            var shape = smoke.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = width * 0.2f;
            
            var velocityOverLifetime = smoke.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            
            var sizeOverLifetime = smoke.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.3f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 1.5f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            var colorOverLifetime = smoke.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.3f, 0.3f, 0.3f), 0.0f), new GradientColorKey(new Color(0.6f, 0.6f, 0.6f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.6f, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material smokeMaterial = CreateSmokeTrailMaterial();
            var renderer = smoke.GetComponent<ParticleSystemRenderer>();
            renderer.material = smokeMaterial;
        }
        
        private static void CreateFireTrail(GameObject parent, float width, float time)
        {
            // Fire trail with flames
            GameObject trailObj = new GameObject("FireTrail");
            trailObj.transform.SetParent(parent.transform);
            ParticleSystem fire = trailObj.AddComponent<ParticleSystem>();
            
            var main = fire.main;
            main.startLifetime = time;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 4.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(width * 0.4f, width * 1.2f);
            main.startColor = new Color(1.0f, 0.5f, 0.1f, 0.9f);
            main.maxParticles = 120;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = fire.emission;
            emission.rateOverTime = 40;
            
            var shape = fire.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = width * 0.3f;
            
            var velocityOverLifetime = fire.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var colorOverLifetime = fire.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(1.0f, 0.8f, 0.2f), 0.0f), new GradientColorKey(new Color(1.0f, 0.2f, 0.0f), 0.5f), new GradientColorKey(new Color(0.3f, 0.1f, 0.0f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.9f, 0.0f), new GradientAlphaKey(0.7f, 0.5f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            var textureSheetAnimation = fire.textureSheetAnimation;
            textureSheetAnimation.enabled = true;
            textureSheetAnimation.numTilesX = 4;
            textureSheetAnimation.numTilesY = 4;
            textureSheetAnimation.animation = ParticleSystemAnimationType.WholeSheet;
            textureSheetAnimation.frameOverTime = new ParticleSystem.MinMaxCurve(1.0f);
            
            Material fireMaterial = CreateFireTrailMaterial();
            var renderer = fire.GetComponent<ParticleSystemRenderer>();
            renderer.material = fireMaterial;
        }
        
        private static void CreateLightningTrail(GameObject parent, float width, float time)
        {
            // Lightning trail with electric bolts
            GameObject trailObj = new GameObject("LightningTrail");
            trailObj.transform.SetParent(parent.transform);
            
            TrailRenderer trail = trailObj.AddComponent<TrailRenderer>();
            trail.time = time * 0.3f;
            trail.startWidth = width;
            trail.endWidth = 0.02f;
            trail.numCornerVertices = 2;
            trail.numCapVertices = 2;
            // Fixed: useWorldSpace deprecated in Unity 6.2 TrailRenderer
            // trail.useWorldSpace = true;
            
            Material lightningMaterial = CreateLightningTrailMaterial();
            trail.material = lightningMaterial;
            
            // Lightning bolts
            ParticleSystem bolts = trailObj.AddComponent<ParticleSystem>();
            var main = bolts.main;
            main.startLifetime = 0.1f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(5.0f, 12.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(0.02f, 0.1f);
            main.startColor = new Color(0.8f, 0.9f, 1.0f, 1.0f);
            main.maxParticles = 40;
            
            var emission = bolts.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 10),
                new ParticleSystem.Burst(0.1f, 8),
                new ParticleSystem.Burst(0.2f, 6)
            });
            
            var noise = bolts.noise;
            noise.enabled = true;
            noise.strength = 5.0f;
            noise.frequency = 10.0f;
            
            Material boltMaterial = CreateLightningBoltMaterial();
            var boltRenderer = bolts.GetComponent<ParticleSystemRenderer>();
            boltRenderer.material = boltMaterial;
            
            // Add light component
            Light lightningLight = trailObj.AddComponent<Light>();
            lightningLight.type = LightType.Point;
            lightningLight.color = new Color(0.8f, 0.9f, 1.0f);
            lightningLight.intensity = 2.0f;
            lightningLight.range = 5.0f;
        }
        
        private static string ModifyTrailEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject trailEffect = GameObject.Find(effectName);
            if (trailEffect == null)
                return JsonHelper.CreateErrorResponse($"Trail effect '{effectName}' not found");
            
            float width = (float)(parameters["width"] ?? 1.0f);
            float time = (float)(parameters["time"] ?? 1.0f);
            
            // Modify trail renderers
            TrailRenderer[] trails = trailEffect.GetComponentsInChildren<TrailRenderer>();
            foreach (var trail in trails)
            {
                trail.startWidth = width;
                trail.time = time;
            }
            
            // Modify particle systems
            ParticleSystem[] particleSystems = trailEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                main.startLifetime = time;
                main.startSize = new ParticleSystem.MinMaxCurve(width * 0.3f, width * 1.2f);
            }
            
            return JsonHelper.CreateSuccessResponse($"Trail effect '{effectName}' modified successfully");
        }
        
        private static string DeleteTrailEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject trailEffect = GameObject.Find(effectName);
            if (trailEffect == null)
                return JsonHelper.CreateErrorResponse($"Trail effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(trailEffect);
            return JsonHelper.CreateSuccessResponse($"Trail effect '{effectName}' deleted successfully");
        }
        
        private static string GetTrailEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject trailEffect = GameObject.Find(effectName);
            if (trailEffect == null)
                return JsonHelper.CreateErrorResponse($"Trail effect '{effectName}' not found");
            
            var properties = new
            {
                name = trailEffect.name,
                position = trailEffect.transform.position,
                rotation = trailEffect.transform.rotation,
                scale = trailEffect.transform.localScale,
                trailRenderers = trailEffect.GetComponentsInChildren<TrailRenderer>().Length,
                particleSystems = trailEffect.GetComponentsInChildren<ParticleSystem>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("Trail effect properties retrieved", properties);
        }
        
        // Material creation methods for trail effects
        private static Material CreateWeaponTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "WeaponTrailMaterial";
            material.color = new Color(0.8f, 0.8f, 1.0f, 0.8f);
            return material;
        }
        
        private static Material CreateTrailSparkMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "TrailSparkMaterial";
            material.color = new Color(1.0f, 0.8f, 0.2f, 1.0f);
            return material;
        }
        
        private static Material CreateMovementTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "MovementTrailMaterial";
            material.color = new Color(0.2f, 0.6f, 1.0f, 0.8f);
            return material;
        }
        
        private static Material CreateMagicTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "MagicTrailMaterial";
            material.color = new Color(0.8f, 0.2f, 1.0f, 0.9f);
            return material;
        }
        
        private static Material CreateMagicParticleMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "MagicParticleMaterial";
            material.color = new Color(0.8f, 0.2f, 1.0f, 0.9f);
            return material;
        }
        
        private static Material CreateProjectileTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ProjectileTrailMaterial";
            material.color = new Color(1.0f, 0.9f, 0.6f, 0.9f);
            return material;
        }
        
        private static Material CreateSpeedLineMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "SpeedLineMaterial";
            material.color = new Color(1.0f, 1.0f, 0.8f, 0.7f);
            return material;
        }
        
        private static Material CreateEnergyTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "EnergyTrailMaterial";
            material.color = new Color(0.2f, 0.8f, 1.0f, 1.0f);
            return material;
        }
        
        private static Material CreateElectricArcMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ElectricArcMaterial";
            material.color = new Color(0.2f, 0.8f, 1.0f, 1.0f);
            return material;
        }
        
        private static Material CreateSmokeTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "SmokeTrailMaterial";
            material.color = new Color(0.3f, 0.3f, 0.3f, 0.6f);
            return material;
        }
        
        private static Material CreateFireTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "FireTrailMaterial";
            material.color = new Color(1.0f, 0.5f, 0.1f, 0.9f);
            return material;
        }
        
        private static Material CreateLightningTrailMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "LightningTrailMaterial";
            material.color = new Color(0.8f, 0.9f, 1.0f, 1.0f);
            return material;
        }
        
        private static Material CreateLightningBoltMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "LightningBoltMaterial";
            material.color = new Color(0.8f, 0.9f, 1.0f, 1.0f);
            return material;
        }

        private static string SetupDistortionEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateDistortionEffect(parameters);
                    case "modify":
                        return ModifyDistortionEffect(parameters);
                    case "delete":
                        return DeleteDistortionEffect(parameters);
                    case "get_properties":
                        return GetDistortionEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"SetupDistortionEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error setting up distortion effects: {ex.Message}");
            }
        }
        
        private static string CreateDistortionEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "DistortionEffect";
            string distortionType = parameters["distortion_type"]?.ToString() ?? "heat";
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            float size = (float)(parameters["size"] ?? 1.0f);
            
            GameObject distortionEffect = new GameObject(effectName);
            
            switch (distortionType.ToLower())
            {
                case "heat":
                    CreateHeatDistortion(distortionEffect, intensity, size);
                    break;
                case "portal":
                    CreatePortalDistortion(distortionEffect, intensity, size);
                    break;
                case "water":
                    CreateWaterDistortion(distortionEffect, intensity, size);
                    break;
                case "glass":
                    CreateGlassDistortion(distortionEffect, intensity, size);
                    break;
                case "energy":
                    CreateEnergyDistortion(distortionEffect, intensity, size);
                    break;
                case "warp":
                    CreateWarpDistortion(distortionEffect, intensity, size);
                    break;
                case "ripple":
                    CreateRippleDistortion(distortionEffect, intensity, size);
                    break;
                case "vortex":
                    CreateVortexDistortion(distortionEffect, intensity, size);
                    break;
                default:
                    CreateHeatDistortion(distortionEffect, intensity, size);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"Distortion effect '{effectName}' of type '{distortionType}' created successfully");
        }
        
        private static void CreateHeatDistortion(GameObject parent, float intensity, float size)
        {
            // Heat distortion using particle system
            GameObject distortionObj = new GameObject("HeatDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem heatPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = heatPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.5f, size * 1.5f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, 0.3f * intensity);
            main.maxParticles = 50;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = heatPS.emission;
            emission.rateOverTime = 15;
            
            var shape = heatPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(size, size * 2.0f, size);
            
            var velocityOverLifetime = heatPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var noise = heatPS.noise;
            noise.enabled = true;
            noise.strength = intensity * 2.0f;
            noise.frequency = 0.5f;
            noise.scrollSpeed = 1.0f;
            
            var sizeOverLifetime = heatPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 1.5f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            Material heatMaterial = CreateHeatDistortionMaterial();
            var renderer = heatPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = heatMaterial;
        }
        
        private static void CreatePortalDistortion(GameObject parent, float intensity, float size)
        {
            // Portal distortion with swirling effect
            GameObject distortionObj = new GameObject("PortalDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem portalPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = portalPS.main;
            main.startLifetime = 3.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.3f, size * 1.0f);
            main.startColor = new Color(0.5f, 0.2f, 1.0f, 0.6f * intensity);
            main.maxParticles = 100;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = portalPS.emission;
            emission.rateOverTime = 25;
            
            var shape = portalPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = size;
            
            var velocityOverLifetime = portalPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(-2.0f * intensity, -5.0f * intensity);
            
            var rotationOverLifetime = portalPS.rotationOverLifetime;
            rotationOverLifetime.enabled = true;
            rotationOverLifetime.z = new ParticleSystem.MinMaxCurve(90.0f * intensity, 180.0f * intensity);
            
            var colorOverLifetime = portalPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.5f, 0.2f, 1.0f), 0.0f), new GradientColorKey(new Color(0.2f, 0.1f, 0.8f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.6f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material portalMaterial = CreatePortalDistortionMaterial();
            var renderer = portalPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = portalMaterial;
        }
        
        private static void CreateWaterDistortion(GameObject parent, float intensity, float size)
        {
            // Water distortion with wave-like movement
            GameObject distortionObj = new GameObject("WaterDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem waterPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = waterPS.main;
            main.startLifetime = 4.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.2f, 1.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.8f, size * 2.0f);
            main.startColor = new Color(0.2f, 0.6f, 1.0f, 0.4f * intensity);
            main.maxParticles = 80;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = waterPS.emission;
            emission.rateOverTime = 20;
            
            var shape = waterPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(size * 2.0f, 0.1f, size * 2.0f);
            
            var velocityOverLifetime = waterPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-1.0f, 1.0f);
            
            var noise = waterPS.noise;
            noise.enabled = true;
            noise.strength = intensity;
            noise.frequency = 0.3f;
            noise.scrollSpeed = 0.5f;
            
            var textureSheetAnimation = waterPS.textureSheetAnimation;
            textureSheetAnimation.enabled = true;
            textureSheetAnimation.numTilesX = 4;
            textureSheetAnimation.numTilesY = 4;
            textureSheetAnimation.animation = ParticleSystemAnimationType.WholeSheet;
            textureSheetAnimation.frameOverTime = new ParticleSystem.MinMaxCurve(0.5f);
            
            Material waterMaterial = CreateWaterDistortionMaterial();
            var renderer = waterPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = waterMaterial;
        }
        
        private static void CreateGlassDistortion(GameObject parent, float intensity, float size)
        {
            // Glass distortion with refraction effect
            GameObject distortionObj = new GameObject("GlassDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem glassPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = glassPS.main;
            main.startLifetime = 1.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.0f, 0.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.5f, size * 1.0f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, 0.8f * intensity);
            main.maxParticles = 30;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = glassPS.emission;
            emission.rateOverTime = 10;
            
            var shape = glassPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(size, size, 0.1f);
            
            var rotationOverLifetime = glassPS.rotationOverLifetime;
            rotationOverLifetime.enabled = true;
            rotationOverLifetime.z = new ParticleSystem.MinMaxCurve(-30.0f, 30.0f);
            
            Material glassMaterial = CreateGlassDistortionMaterial();
            var renderer = glassPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = glassMaterial;
        }
        
        private static void CreateEnergyDistortion(GameObject parent, float intensity, float size)
        {
            // Energy distortion with electric field effect
            GameObject distortionObj = new GameObject("EnergyDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem energyPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = energyPS.main;
            main.startLifetime = 1.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.2f, size * 0.8f);
            main.startColor = new Color(0.2f, 0.8f, 1.0f, 0.7f * intensity);
            main.maxParticles = 60;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = energyPS.emission;
            emission.rateOverTime = 30;
            
            var shape = energyPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = size;
            
            var velocityOverLifetime = energyPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(2.0f * intensity, 5.0f * intensity);
            
            var noise = energyPS.noise;
            noise.enabled = true;
            noise.strength = intensity * 3.0f;
            noise.frequency = 2.0f;
            noise.scrollSpeed = 2.0f;
            
            var colorOverLifetime = energyPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.2f, 0.8f, 1.0f), 0.0f), new GradientColorKey(new Color(0.8f, 0.9f, 1.0f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.7f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material energyMaterial = CreateEnergyDistortionMaterial();
            var renderer = energyPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = energyMaterial;
        }
        
        private static void CreateWarpDistortion(GameObject parent, float intensity, float size)
        {
            // Warp distortion with space-time bending effect
            GameObject distortionObj = new GameObject("WarpDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem warpPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = warpPS.main;
            main.startLifetime = 2.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.1f, size * 1.5f);
            main.startColor = new Color(0.8f, 0.3f, 0.9f, 0.5f * intensity);
            main.maxParticles = 70;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = warpPS.emission;
            emission.rateOverTime = 20;
            
            var shape = warpPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Donut;
            shape.radius = size;
            shape.donutRadius = 0.3f;
            
            var velocityOverLifetime = warpPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.orbitalX = new ParticleSystem.MinMaxCurve(45.0f * intensity, 90.0f * intensity);
            velocityOverLifetime.orbitalY = new ParticleSystem.MinMaxCurve(30.0f * intensity, 60.0f * intensity);
            
            var sizeOverLifetime = warpPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.2f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 0.1f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            Material warpMaterial = CreateWarpDistortionMaterial();
            var renderer = warpPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = warpMaterial;
        }
        
        private static void CreateRippleDistortion(GameObject parent, float intensity, float size)
        {
            // Ripple distortion with expanding waves
            GameObject distortionObj = new GameObject("RippleDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem ripplePS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = ripplePS.main;
            main.startLifetime = 3.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.0f, 0.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.2f, size * 0.5f);
            main.startColor = new Color(1.0f, 1.0f, 1.0f, 0.6f * intensity);
            main.maxParticles = 40;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = ripplePS.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 5),
                new ParticleSystem.Burst(0.5f, 4),
                new ParticleSystem.Burst(1.0f, 3)
            });
            
            var shape = ripplePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.1f;
            
            var sizeOverLifetime = ripplePS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.2f);
            sizeCurve.AddKey(1f, size * 2.0f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            var colorOverLifetime = ripplePS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(1.0f, 1.0f, 1.0f), 0.0f), new GradientColorKey(new Color(0.8f, 0.8f, 1.0f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.6f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material rippleMaterial = CreateRippleDistortionMaterial();
            var renderer = ripplePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = rippleMaterial;
        }
        
        private static void CreateVortexDistortion(GameObject parent, float intensity, float size)
        {
            // Vortex distortion with spinning effect
            GameObject distortionObj = new GameObject("VortexDistortion");
            distortionObj.transform.SetParent(parent.transform);
            ParticleSystem vortexPS = distortionObj.AddComponent<ParticleSystem>();
            
            var main = vortexPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 4.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.1f, size * 0.6f);
            main.startColor = new Color(0.6f, 0.2f, 0.8f, 0.7f * intensity);
            main.maxParticles = 90;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = vortexPS.emission;
            emission.rateOverTime = 35;
            
            var shape = vortexPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = size;
            
            var velocityOverLifetime = vortexPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.orbitalZ = new ParticleSystem.MinMaxCurve(180.0f * intensity, 360.0f * intensity);
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(-1.0f * intensity, -3.0f * intensity);
            
            var rotationOverLifetime = vortexPS.rotationOverLifetime;
            rotationOverLifetime.enabled = true;
            rotationOverLifetime.z = new ParticleSystem.MinMaxCurve(90.0f * intensity, 270.0f * intensity);
            
            var colorOverLifetime = vortexPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.6f, 0.2f, 0.8f), 0.0f), new GradientColorKey(new Color(0.3f, 0.1f, 0.6f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.7f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material vortexMaterial = CreateVortexDistortionMaterial();
            var renderer = vortexPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = vortexMaterial;
        }
        
        private static string ModifyDistortionEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject distortionEffect = GameObject.Find(effectName);
            if (distortionEffect == null)
                return JsonHelper.CreateErrorResponse($"Distortion effect '{effectName}' not found");
            
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            float size = (float)(parameters["size"] ?? 1.0f);
            
            // Modify particle systems
            ParticleSystem[] particleSystems = distortionEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                var startColor = main.startColor.color;
                startColor.a = startColor.a * intensity;
                main.startColor = startColor;
                
                var shape = ps.shape;
                if (shape.enabled)
                {
                    if (shape.shapeType == ParticleSystemShapeType.Box)
                        shape.scale = new Vector3(size, size * 2.0f, size);
                    else if (shape.shapeType == ParticleSystemShapeType.Circle || shape.shapeType == ParticleSystemShapeType.Sphere)
                        shape.radius = size;
                }
            }
            
            return JsonHelper.CreateSuccessResponse($"Distortion effect '{effectName}' modified successfully");
        }
        
        private static string DeleteDistortionEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject distortionEffect = GameObject.Find(effectName);
            if (distortionEffect == null)
                return JsonHelper.CreateErrorResponse($"Distortion effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(distortionEffect);
            return JsonHelper.CreateSuccessResponse($"Distortion effect '{effectName}' deleted successfully");
        }
        
        private static string GetDistortionEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject distortionEffect = GameObject.Find(effectName);
            if (distortionEffect == null)
                return JsonHelper.CreateErrorResponse($"Distortion effect '{effectName}' not found");
            
            var properties = new
            {
                name = distortionEffect.name,
                position = distortionEffect.transform.position,
                rotation = distortionEffect.transform.rotation,
                scale = distortionEffect.transform.localScale,
                particleSystems = distortionEffect.GetComponentsInChildren<ParticleSystem>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("Distortion effect properties retrieved", properties);
        }
        
        // Material creation methods for distortion effects
        private static Material CreateHeatDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "HeatDistortionMaterial";
            material.color = new Color(1.0f, 0.8f, 0.6f, 0.3f);
            return material;
        }
        
        private static Material CreatePortalDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "PortalDistortionMaterial";
            material.color = new Color(0.5f, 0.2f, 1.0f, 0.6f);
            return material;
        }
        
        private static Material CreateWaterDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "WaterDistortionMaterial";
            material.color = new Color(0.2f, 0.6f, 1.0f, 0.4f);
            return material;
        }
        
        private static Material CreateGlassDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "GlassDistortionMaterial";
            material.color = new Color(1.0f, 1.0f, 1.0f, 0.8f);
            return material;
        }
        
        private static Material CreateEnergyDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "EnergyDistortionMaterial";
            material.color = new Color(0.2f, 0.8f, 1.0f, 0.7f);
            return material;
        }
        
        private static Material CreateWarpDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "WarpDistortionMaterial";
            material.color = new Color(0.8f, 0.3f, 0.9f, 0.5f);
            return material;
        }
        
        private static Material CreateRippleDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "RippleDistortionMaterial";
            material.color = new Color(1.0f, 1.0f, 1.0f, 0.6f);
            return material;
        }
        
        private static Material CreateVortexDistortionMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "VortexDistortionMaterial";
            material.color = new Color(0.6f, 0.2f, 0.8f, 0.7f);
            return material;
        }

        private static string CreateHologramEffects(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "create";
                
                switch (action.ToLower())
                {
                    case "create":
                        return CreateHologramEffect(parameters);
                    case "modify":
                        return ModifyHologramEffect(parameters);
                    case "delete":
                        return DeleteHologramEffect(parameters);
                    case "get_properties":
                        return GetHologramEffectProperties(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"CreateHologramEffects error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error creating hologram effects: {ex.Message}");
            }
        }
        
        private static string CreateHologramEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString() ?? "HologramEffect";
            string hologramType = parameters["hologram_type"]?.ToString() ?? "standard";
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            float size = (float)(parameters["size"] ?? 1.0f);
            
            GameObject hologramEffect = new GameObject(effectName);
            
            switch (hologramType.ToLower())
            {
                case "standard":
                    CreateStandardHologram(hologramEffect, intensity, size);
                    break;
                case "glitch":
                    CreateGlitchHologram(hologramEffect, intensity, size);
                    break;
                case "scan":
                    CreateScanHologram(hologramEffect, intensity, size);
                    break;
                case "projection":
                    CreateProjectionHologram(hologramEffect, intensity, size);
                    break;
                case "data":
                    CreateDataHologram(hologramEffect, intensity, size);
                    break;
                case "interference":
                    CreateInterferenceHologram(hologramEffect, intensity, size);
                    break;
                case "matrix":
                    CreateMatrixHologram(hologramEffect, intensity, size);
                    break;
                case "wireframe":
                    CreateWireframeHologram(hologramEffect, intensity, size);
                    break;
                default:
                    CreateStandardHologram(hologramEffect, intensity, size);
                    break;
            }
            
            return JsonHelper.CreateSuccessResponse($"Hologram effect '{effectName}' of type '{hologramType}' created successfully");
        }
        
        private static void CreateStandardHologram(GameObject parent, float intensity, float size)
        {
            // Standard hologram with blue tint and flickering
            GameObject hologramObj = new GameObject("StandardHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem hologramPS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = hologramPS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.1f, 0.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.05f, size * 0.2f);
            main.startColor = new Color(0.2f, 0.6f, 1.0f, 0.7f * intensity);
            main.maxParticles = 100;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = hologramPS.emission;
            emission.rateOverTime = 30;
            
            var shape = hologramPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(size, size * 1.5f, size * 0.1f);
            
            var velocityOverLifetime = hologramPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            
            var colorOverLifetime = hologramPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.2f, 0.6f, 1.0f), 0.0f), new GradientColorKey(new Color(0.1f, 0.4f, 0.8f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.7f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            // Add light component
            Light hologramLight = hologramObj.AddComponent<Light>();
            hologramLight.type = LightType.Point;
            hologramLight.color = new Color(0.2f, 0.6f, 1.0f);
            hologramLight.intensity = intensity * 2.0f;
            hologramLight.range = size * 3.0f;
            
            Material hologramMaterial = CreateStandardHologramMaterial();
            var renderer = hologramPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = hologramMaterial;
        }
        
        private static void CreateGlitchHologram(GameObject parent, float intensity, float size)
        {
            // Glitch hologram with distortion and color shifts
            GameObject hologramObj = new GameObject("GlitchHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem glitchPS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = glitchPS.main;
            main.startLifetime = 1.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.2f, 1.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.1f, size * 0.3f);
            main.startColor = new Color(1.0f, 0.2f, 0.8f, 0.8f * intensity);
            main.maxParticles = 150;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = glitchPS.emission;
            emission.rateOverTime = 50;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 20),
                new ParticleSystem.Burst(0.3f, 15),
                new ParticleSystem.Burst(0.7f, 10)
            });
            
            var shape = glitchPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(size * 1.2f, size * 1.8f, 0.1f);
            
            var velocityOverLifetime = glitchPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-2.0f, 2.0f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-1.0f, 3.0f);
            
            var noise = glitchPS.noise;
            noise.enabled = true;
            noise.strength = intensity * 5.0f;
            noise.frequency = 3.0f;
            noise.scrollSpeed = 5.0f;
            
            var colorOverLifetime = glitchPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { 
                    new GradientColorKey(new Color(1.0f, 0.2f, 0.8f), 0.0f), 
                    new GradientColorKey(new Color(0.2f, 1.0f, 0.3f), 0.3f),
                    new GradientColorKey(new Color(0.8f, 0.1f, 0.2f), 0.7f),
                    new GradientColorKey(new Color(0.1f, 0.3f, 1.0f), 1.0f) 
                },
                new GradientAlphaKey[] { new GradientAlphaKey(0.8f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material glitchMaterial = CreateGlitchHologramMaterial();
            var renderer = glitchPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = glitchMaterial;
        }
        
        private static void CreateScanHologram(GameObject parent, float intensity, float size)
        {
            // Scan hologram with sweeping lines
            GameObject hologramObj = new GameObject("ScanHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem scanPS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = scanPS.main;
            main.startLifetime = 3.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.0f, 0.2f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.02f, size * 0.1f);
            main.startColor = new Color(0.1f, 1.0f, 0.3f, 0.9f * intensity);
            main.maxParticles = 200;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = scanPS.emission;
            emission.rateOverTime = 60;
            
            var shape = scanPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(size * 2.0f, 0.05f, size * 0.05f);
            
            var velocityOverLifetime = scanPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(size * 0.5f, size * 1.0f);
            
            var sizeOverLifetime = scanPS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.5f);
            sizeCurve.AddKey(0.1f, 1f);
            sizeCurve.AddKey(0.9f, 1f);
            sizeCurve.AddKey(1f, 0.2f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            Material scanMaterial = CreateScanHologramMaterial();
            var renderer = scanPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = scanMaterial;
        }
        
        private static void CreateProjectionHologram(GameObject parent, float intensity, float size)
        {
            // Projection hologram with beam effect
            GameObject hologramObj = new GameObject("ProjectionHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem projectionPS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = projectionPS.main;
            main.startLifetime = 2.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.1f, size * 0.4f);
            main.startColor = new Color(0.8f, 0.9f, 1.0f, 0.6f * intensity);
            main.maxParticles = 80;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = projectionPS.emission;
            emission.rateOverTime = 25;
            
            var shape = projectionPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Cone;
            shape.angle = 15.0f;
            shape.radius = size * 0.1f;
            
            var velocityOverLifetime = projectionPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(2.0f, 5.0f);
            
            var colorOverLifetime = projectionPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.8f, 0.9f, 1.0f), 0.0f), new GradientColorKey(new Color(0.4f, 0.6f, 0.9f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.6f * intensity, 0.0f), new GradientAlphaKey(0.1f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material projectionMaterial = CreateProjectionHologramMaterial();
            var renderer = projectionPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = projectionMaterial;
        }
        
        private static void CreateDataHologram(GameObject parent, float intensity, float size)
        {
            // Data hologram with flowing information streams
            GameObject hologramObj = new GameObject("DataHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem dataPS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = dataPS.main;
            main.startLifetime = 4.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.5f, 2.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.02f, size * 0.08f);
            main.startColor = new Color(0.3f, 0.9f, 0.6f, 0.8f * intensity);
            main.maxParticles = 300;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = dataPS.emission;
            emission.rateOverTime = 75;
            
            var shape = dataPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(size * 0.1f, size * 2.0f, size * 0.1f);
            
            var velocityOverLifetime = dataPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(1.0f, 3.0f);
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.2f, 0.2f);
            
            var textureSheetAnimation = dataPS.textureSheetAnimation;
            textureSheetAnimation.enabled = true;
            textureSheetAnimation.numTilesX = 8;
            textureSheetAnimation.numTilesY = 8;
            textureSheetAnimation.animation = ParticleSystemAnimationType.WholeSheet;
            textureSheetAnimation.frameOverTime = new ParticleSystem.MinMaxCurve(2.0f);
            
            Material dataMaterial = CreateDataHologramMaterial();
            var renderer = dataPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = dataMaterial;
        }
        
        private static void CreateInterferenceHologram(GameObject parent, float intensity, float size)
        {
            // Interference hologram with wave patterns
            GameObject hologramObj = new GameObject("InterferenceHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem interferencePS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = interferencePS.main;
            main.startLifetime = 3.5f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.1f, 0.8f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.3f, size * 0.8f);
            main.startColor = new Color(0.6f, 0.3f, 1.0f, 0.5f * intensity);
            main.maxParticles = 60;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = interferencePS.emission;
            emission.rateOverTime = 15;
            
            var shape = interferencePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = size * 0.5f;
            
            var velocityOverLifetime = interferencePS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(0.5f, 1.5f);
            
            var noise = interferencePS.noise;
            noise.enabled = true;
            noise.strength = intensity * 2.0f;
            noise.frequency = 1.0f;
            noise.scrollSpeed = 2.0f;
            
            var sizeOverLifetime = interferencePS.sizeOverLifetime;
            sizeOverLifetime.enabled = true;
            AnimationCurve sizeCurve = new AnimationCurve();
            sizeCurve.AddKey(0f, 0.2f);
            sizeCurve.AddKey(0.5f, 1f);
            sizeCurve.AddKey(1f, 0.3f);
            sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            
            Material interferenceMaterial = CreateInterferenceHologramMaterial();
            var renderer = interferencePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = interferenceMaterial;
        }
        
        private static void CreateMatrixHologram(GameObject parent, float intensity, float size)
        {
            // Matrix-style hologram with digital rain effect
            GameObject hologramObj = new GameObject("MatrixHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem matrixPS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = matrixPS.main;
            main.startLifetime = 5.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(1.0f, 4.0f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.05f, size * 0.15f);
            main.startColor = new Color(0.1f, 1.0f, 0.1f, 0.9f * intensity);
            main.maxParticles = 400;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = matrixPS.emission;
            emission.rateOverTime = 100;
            
            var shape = matrixPS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(size * 3.0f, 0.1f, size * 0.1f);
            
            var velocityOverLifetime = matrixPS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-2.0f, -6.0f);
            
            var textureSheetAnimation = matrixPS.textureSheetAnimation;
            textureSheetAnimation.enabled = true;
            textureSheetAnimation.numTilesX = 10;
            textureSheetAnimation.numTilesY = 10;
            textureSheetAnimation.animation = ParticleSystemAnimationType.WholeSheet;
            textureSheetAnimation.frameOverTime = new ParticleSystem.MinMaxCurve(3.0f);
            
            var colorOverLifetime = matrixPS.colorOverLifetime;
            colorOverLifetime.enabled = true;
            Gradient gradient = new Gradient();
            gradient.SetKeys(
                new GradientColorKey[] { new GradientColorKey(new Color(0.1f, 1.0f, 0.1f), 0.0f), new GradientColorKey(new Color(0.05f, 0.6f, 0.05f), 1.0f) },
                new GradientAlphaKey[] { new GradientAlphaKey(0.9f * intensity, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
            );
            colorOverLifetime.color = gradient;
            
            Material matrixMaterial = CreateMatrixHologramMaterial();
            var renderer = matrixPS.GetComponent<ParticleSystemRenderer>();
            renderer.material = matrixMaterial;
        }
        
        private static void CreateWireframeHologram(GameObject parent, float intensity, float size)
        {
            // Wireframe hologram with geometric lines
            GameObject hologramObj = new GameObject("WireframeHologram");
            hologramObj.transform.SetParent(parent.transform);
            ParticleSystem wireframePS = hologramObj.AddComponent<ParticleSystem>();
            
            var main = wireframePS.main;
            main.startLifetime = 2.0f;
            main.startSpeed = new ParticleSystem.MinMaxCurve(0.0f, 0.5f);
            main.startSize = new ParticleSystem.MinMaxCurve(size * 0.02f, size * 0.1f);
            main.startColor = new Color(0.4f, 0.8f, 1.0f, 0.8f * intensity);
            main.maxParticles = 120;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            
            var emission = wireframePS.emission;
            emission.rateOverTime = 40;
            
            var shape = wireframePS.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(size, size, size);
            
            var velocityOverLifetime = wireframePS.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.x = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            velocityOverLifetime.z = new ParticleSystem.MinMaxCurve(-0.5f, 0.5f);
            
            var rotationOverLifetime = wireframePS.rotationOverLifetime;
            rotationOverLifetime.enabled = true;
            rotationOverLifetime.x = new ParticleSystem.MinMaxCurve(30.0f, 90.0f);
            rotationOverLifetime.y = new ParticleSystem.MinMaxCurve(30.0f, 90.0f);
            rotationOverLifetime.z = new ParticleSystem.MinMaxCurve(30.0f, 90.0f);
            
            Material wireframeMaterial = CreateWireframeHologramMaterial();
            var renderer = wireframePS.GetComponent<ParticleSystemRenderer>();
            renderer.material = wireframeMaterial;
        }
        
        private static string ModifyHologramEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for modification");
            
            GameObject hologramEffect = GameObject.Find(effectName);
            if (hologramEffect == null)
                return JsonHelper.CreateErrorResponse($"Hologram effect '{effectName}' not found");
            
            float intensity = (float)(parameters["intensity"] ?? 1.0f);
            float size = (float)(parameters["size"] ?? 1.0f);
            
            // Modify particle systems
            ParticleSystem[] particleSystems = hologramEffect.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in particleSystems)
            {
                var main = ps.main;
                var startColor = main.startColor.color;
                startColor.a = startColor.a * intensity;
                main.startColor = startColor;
                
                var shape = ps.shape;
                if (shape.enabled)
                {
                    if (shape.shapeType == ParticleSystemShapeType.Box)
                        shape.scale = new Vector3(size, size * 1.5f, size * 0.1f);
                    else if (shape.shapeType == ParticleSystemShapeType.Circle || shape.shapeType == ParticleSystemShapeType.Sphere)
                        shape.radius = size;
                }
            }
            
            // Modify lights
            Light[] lights = hologramEffect.GetComponentsInChildren<Light>();
            foreach (var light in lights)
            {
                light.intensity = intensity * 2.0f;
                light.range = size * 3.0f;
            }
            
            return JsonHelper.CreateSuccessResponse($"Hologram effect '{effectName}' modified successfully");
        }
        
        private static string DeleteHologramEffect(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required for deletion");
            
            GameObject hologramEffect = GameObject.Find(effectName);
            if (hologramEffect == null)
                return JsonHelper.CreateErrorResponse($"Hologram effect '{effectName}' not found");
            
            UnityEngine.Object.DestroyImmediate(hologramEffect);
            return JsonHelper.CreateSuccessResponse($"Hologram effect '{effectName}' deleted successfully");
        }
        
        private static string GetHologramEffectProperties(JObject parameters)
        {
            string effectName = parameters["name"]?.ToString();
            if (string.IsNullOrEmpty(effectName))
                return JsonHelper.CreateErrorResponse("Effect name is required");
            
            GameObject hologramEffect = GameObject.Find(effectName);
            if (hologramEffect == null)
                return JsonHelper.CreateErrorResponse($"Hologram effect '{effectName}' not found");
            
            var properties = new
            {
                name = hologramEffect.name,
                position = hologramEffect.transform.position,
                rotation = hologramEffect.transform.rotation,
                scale = hologramEffect.transform.localScale,
                particleSystems = hologramEffect.GetComponentsInChildren<ParticleSystem>().Length,
                lights = hologramEffect.GetComponentsInChildren<Light>().Length
            };
            
            return JsonHelper.CreateSuccessResponse("Hologram effect properties retrieved", properties);
        }
        
        // Material creation methods for hologram effects
        private static Material CreateStandardHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "StandardHologramMaterial";
            material.color = new Color(0.2f, 0.6f, 1.0f, 0.7f);
            return material;
        }
        
        private static Material CreateGlitchHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "GlitchHologramMaterial";
            material.color = new Color(1.0f, 0.2f, 0.8f, 0.8f);
            return material;
        }
        
        private static Material CreateScanHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ScanHologramMaterial";
            material.color = new Color(0.1f, 1.0f, 0.3f, 0.9f);
            return material;
        }
        
        private static Material CreateProjectionHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "ProjectionHologramMaterial";
            material.color = new Color(0.8f, 0.9f, 1.0f, 0.6f);
            return material;
        }
        
        private static Material CreateDataHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "DataHologramMaterial";
            material.color = new Color(0.3f, 0.9f, 0.6f, 0.8f);
            return material;
        }
        
        private static Material CreateInterferenceHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "InterferenceHologramMaterial";
            material.color = new Color(0.6f, 0.3f, 1.0f, 0.5f);
            return material;
        }
        
        private static Material CreateMatrixHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "MatrixHologramMaterial";
            material.color = new Color(0.1f, 1.0f, 0.1f, 0.9f);
            return material;
        }
        
        private static Material CreateWireframeHologramMaterial()
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.name = "WireframeHologramMaterial";
            material.color = new Color(0.4f, 0.8f, 1.0f, 0.8f);
            return material;
        }

        private static string OptimizeVfxPerformance(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString() ?? "optimize_all";
                
                switch (action.ToLower())
                {
                    case "optimize_all":
                        return OptimizeAllVfxSystems(parameters);
                    case "reduce_particles":
                        return ReduceParticleCount(parameters);
                    case "optimize_materials":
                        return OptimizeMaterials(parameters);
                    case "cull_distance":
                        return SetupCullingDistance(parameters);
                    case "lod_system":
                        return SetupLodSystem(parameters);
                    case "batch_optimization":
                        return OptimizeBatching(parameters);
                    case "memory_optimization":
                        return OptimizeMemoryUsage(parameters);
                    case "performance_analysis":
                        return AnalyzeVfxPerformance(parameters);
                    default:
                        return JsonHelper.CreateErrorResponse($"Unknown optimization action: {action}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"OptimizeVfxPerformance error: {ex.Message}");
                return JsonHelper.CreateErrorResponse($"Error optimizing VFX performance: {ex.Message}");
            }
        }
        
        private static string OptimizeAllVfxSystems(JObject parameters)
        {
            float qualityLevel = (float)(parameters["quality_level"] ?? 1.0f); // 0.0 = low, 1.0 = high
            bool enableCulling = (bool)(parameters["enable_culling"] ?? true);
            float cullingDistance = (float)(parameters["culling_distance"] ?? 50.0f);
            
            int optimizedSystems = 0;
            ParticleSystem[] allParticleSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            
            foreach (var ps in allParticleSystems)
            {
                OptimizeParticleSystem(ps, qualityLevel, enableCulling, cullingDistance);
                optimizedSystems++;
            }
            
            return JsonHelper.CreateSuccessResponse($"Optimized {optimizedSystems} VFX systems with quality level {qualityLevel}");
        }
        
        private static void OptimizeParticleSystem(ParticleSystem ps, float qualityLevel, bool enableCulling, float cullingDistance)
        {
            var main = ps.main;
            
            // Adjust particle count based on quality level
            main.maxParticles = Mathf.RoundToInt(main.maxParticles * qualityLevel);
            
            // Optimize emission rates
            var emission = ps.emission;
            if (emission.enabled)
            {
                emission.rateOverTime = emission.rateOverTime.constant * qualityLevel;
            }
            
            // Setup culling if enabled
            if (enableCulling)
            {
                var renderer = ps.GetComponent<ParticleSystemRenderer>();
                if (renderer != null)
                {
                    // Add LOD group for distance-based culling
                    LODGroup lodGroup = ps.GetComponent<LODGroup>();
                    if (lodGroup == null)
                    {
                        lodGroup = ps.gameObject.AddComponent<LODGroup>();
                        SetupParticleSystemLOD(lodGroup, ps, cullingDistance);
                    }
                }
            }
            
            // Optimize collision if present
            var collision = ps.collision;
            if (collision.enabled && qualityLevel < 0.7f)
            {
                collision.quality = ParticleSystemCollisionQuality.Low;
                collision.maxCollisionShapes = Mathf.RoundToInt(collision.maxCollisionShapes * qualityLevel);
            }
            
            // Optimize sub-emitters for lower quality
            var subEmitters = ps.subEmitters;
            if (subEmitters.enabled && qualityLevel < 0.5f)
            {
                subEmitters.enabled = false;
            }
        }
        
        private static void SetupParticleSystemLOD(LODGroup lodGroup, ParticleSystem ps, float cullingDistance)
        {
            LOD[] lods = new LOD[3];
            
            // High detail LOD (0-30% of culling distance)
            lods[0] = new LOD(0.3f, new Renderer[] { ps.GetComponent<ParticleSystemRenderer>() });
            
            // Medium detail LOD (30-60% of culling distance)
            var mediumPS = UnityEngine.Object.Instantiate(ps.gameObject).GetComponent<ParticleSystem>();
            var mediumMain = mediumPS.main;
            mediumMain.maxParticles = Mathf.RoundToInt(mediumMain.maxParticles * 0.5f);
            mediumPS.gameObject.SetActive(false);
            lods[1] = new LOD(0.6f, new Renderer[] { mediumPS.GetComponent<ParticleSystemRenderer>() });
            
            // Low detail LOD (60-100% of culling distance)
            var lowPS = UnityEngine.Object.Instantiate(ps.gameObject).GetComponent<ParticleSystem>();
            var lowMain = lowPS.main;
            lowMain.maxParticles = Mathf.RoundToInt(lowMain.maxParticles * 0.2f);
            lowPS.gameObject.SetActive(false);
            lods[2] = new LOD(1.0f, new Renderer[] { lowPS.GetComponent<ParticleSystemRenderer>() });
            
            lodGroup.SetLODs(lods);
            lodGroup.RecalculateBounds();
        }
        
        private static string ReduceParticleCount(JObject parameters)
        {
            float reductionFactor = (float)(parameters["reduction_factor"] ?? 0.5f);
            string targetName = parameters["target"]?.ToString();
            
            int reducedSystems = 0;
            ParticleSystem[] targetSystems;
            
            if (!string.IsNullOrEmpty(targetName))
            {
                GameObject target = GameObject.Find(targetName);
                if (target == null)
                    return JsonHelper.CreateErrorResponse($"Target '{targetName}' not found");
                targetSystems = target.GetComponentsInChildren<ParticleSystem>();
            }
            else
            {
                targetSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            }
            
            foreach (var ps in targetSystems)
            {
                var main = ps.main;
                main.maxParticles = Mathf.RoundToInt(main.maxParticles * reductionFactor);
                
                var emission = ps.emission;
                if (emission.enabled)
                {
                    emission.rateOverTime = emission.rateOverTime.constant * reductionFactor;
                }
                
                reducedSystems++;
            }
            
            return JsonHelper.CreateSuccessResponse($"Reduced particle count for {reducedSystems} systems by factor {reductionFactor}");
        }
        
        private static string OptimizeMaterials(JObject parameters)
        {
            bool enableInstancing = (bool)(parameters["enable_instancing"] ?? true);
            bool simplifyShaders = (bool)(parameters["simplify_shaders"] ?? false);
            
            int optimizedMaterials = 0;
            ParticleSystem[] allParticleSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            
            foreach (var ps in allParticleSystems)
            {
                var renderer = ps.GetComponent<ParticleSystemRenderer>();
                if (renderer != null && renderer.material != null)
                {
                    Material material = renderer.material;
                    
                    // Enable GPU instancing if supported
                    if (enableInstancing && material.enableInstancing == false)
                    {
                        material.enableInstancing = true;
                    }
                    
                    // Simplify shaders for better performance
                    if (simplifyShaders)
                    {
                        if (material.shader.name.Contains("Standard"))
                        {
                            material.shader = Shader.Find("Sprites/Default");
                        }
                    }
                    
                    optimizedMaterials++;
                }
            }
            
            return JsonHelper.CreateSuccessResponse($"Optimized {optimizedMaterials} particle materials");
        }
        
        private static string SetupCullingDistance(JObject parameters)
        {
            float cullingDistance = (float)(parameters["distance"] ?? 50.0f);
            string targetName = parameters["target"]?.ToString();
            
            int culledSystems = 0;
            ParticleSystem[] targetSystems;
            
            if (!string.IsNullOrEmpty(targetName))
            {
                GameObject target = GameObject.Find(targetName);
                if (target == null)
                    return JsonHelper.CreateErrorResponse($"Target '{targetName}' not found");
                targetSystems = target.GetComponentsInChildren<ParticleSystem>();
            }
            else
            {
                targetSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            }
            
            foreach (var ps in targetSystems)
            {
                // Add distance culling script
                DistanceCuller culler = ps.GetComponent<DistanceCuller>();
                if (culler == null)
                {
                    culler = ps.gameObject.AddComponent<DistanceCuller>();
                }
                culler.cullingDistance = cullingDistance;
                culler.particleSystem = ps;
                
                culledSystems++;
            }
            
            return JsonHelper.CreateSuccessResponse($"Setup distance culling for {culledSystems} systems at {cullingDistance} units");
        }
        
        private static string SetupLodSystem(JObject parameters)
        {
            float[] lodDistances = parameters["lod_distances"]?.ToObject<float[]>() ?? new float[] { 0.3f, 0.6f, 1.0f };
            float[] qualityLevels = parameters["quality_levels"]?.ToObject<float[]>() ?? new float[] { 1.0f, 0.5f, 0.2f };
            
            int lodSystems = 0;
            ParticleSystem[] allParticleSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            
            foreach (var ps in allParticleSystems)
            {
                LODGroup lodGroup = ps.GetComponent<LODGroup>();
                if (lodGroup == null)
                {
                    lodGroup = ps.gameObject.AddComponent<LODGroup>();
                }
                
                SetupAdvancedLOD(lodGroup, ps, lodDistances, qualityLevels);
                lodSystems++;
            }
            
            return JsonHelper.CreateSuccessResponse($"Setup LOD system for {lodSystems} particle systems");
        }
        
        private static void SetupAdvancedLOD(LODGroup lodGroup, ParticleSystem originalPS, float[] distances, float[] qualities)
        {
            LOD[] lods = new LOD[distances.Length];
            
            for (int i = 0; i < distances.Length; i++)
            {
                if (i == 0)
                {
                    // Use original for highest quality
                    lods[i] = new LOD(distances[i], new Renderer[] { originalPS.GetComponent<ParticleSystemRenderer>() });
                }
                else
                {
                    // Create optimized versions for lower LODs
                    GameObject lodObject = UnityEngine.Object.Instantiate(originalPS.gameObject);
                    lodObject.name = $"{originalPS.name}_LOD{i}";
                    lodObject.transform.SetParent(originalPS.transform.parent);
                    lodObject.transform.position = originalPS.transform.position;
                    
                    ParticleSystem lodPS = lodObject.GetComponent<ParticleSystem>();
                    var main = lodPS.main;
                    main.maxParticles = Mathf.RoundToInt(main.maxParticles * qualities[i]);
                    
                    var emission = lodPS.emission;
                    if (emission.enabled)
                    {
                        emission.rateOverTime = emission.rateOverTime.constant * qualities[i];
                    }
                    
                    lodObject.SetActive(false);
                    lods[i] = new LOD(distances[i], new Renderer[] { lodPS.GetComponent<ParticleSystemRenderer>() });
                }
            }
            
            lodGroup.SetLODs(lods);
            lodGroup.RecalculateBounds();
        }
        
        private static string OptimizeBatching(JObject parameters)
        {
            bool enableDynamicBatching = (bool)(parameters["dynamic_batching"] ?? true);
            bool enableStaticBatching = (bool)(parameters["static_batching"] ?? true);
            
            // Enable batching in player settings
            if (enableDynamicBatching)
            {
                // Note: This would typically be set in PlayerSettings, but we'll optimize materials instead
                OptimizeForBatching();
            }
            
            return JsonHelper.CreateSuccessResponse("Optimized VFX systems for batching");
        }
        
        private static void OptimizeForBatching()
        {
            ParticleSystem[] allParticleSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            Dictionary<Material, List<ParticleSystemRenderer>> materialGroups = new Dictionary<Material, List<ParticleSystemRenderer>>();
            
            // Group renderers by material
            foreach (var ps in allParticleSystems)
            {
                var renderer = ps.GetComponent<ParticleSystemRenderer>();
                if (renderer != null && renderer.material != null)
                {
                    if (!materialGroups.ContainsKey(renderer.material))
                    {
                        materialGroups[renderer.material] = new List<ParticleSystemRenderer>();
                    }
                    materialGroups[renderer.material].Add(renderer);
                }
            }
            
            // Optimize materials for batching
            foreach (var group in materialGroups)
            {
                if (group.Value.Count > 1)
                {
                    // Enable instancing for materials used by multiple renderers
                    group.Key.enableInstancing = true;
                }
            }
        }
        
        private static string OptimizeMemoryUsage(JObject parameters)
        {
            bool compressTextures = (bool)(parameters["compress_textures"] ?? true);
            bool reduceTextureSize = (bool)(parameters["reduce_texture_size"] ?? false);
            float memoryBudget = (float)(parameters["memory_budget_mb"] ?? 100.0f);
            
            int optimizedTextures = 0;
            ParticleSystem[] allParticleSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            
            foreach (var ps in allParticleSystems)
            {
                var renderer = ps.GetComponent<ParticleSystemRenderer>();
                if (renderer != null && renderer.material != null)
                {
                    Material material = renderer.material;
                    Texture mainTexture = material.mainTexture;
                    
                    if (mainTexture != null)
                    {
                        // Note: In a real implementation, you would modify texture import settings
                        // This is a simplified version for demonstration
                        optimizedTextures++;
                    }
                }
            }
            
            return JsonHelper.CreateSuccessResponse($"Optimized memory usage for {optimizedTextures} textures within {memoryBudget}MB budget");
        }
        
        private static string AnalyzeVfxPerformance(JObject parameters)
        {
            bool includeDetails = (bool)(parameters["include_details"] ?? false);
            
            ParticleSystem[] allParticleSystems = UnityEngine.Object.FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);
            int totalParticles = 0;
            int activeSystems = 0;
            int highParticleCountSystems = 0;
            
            foreach (var ps in allParticleSystems)
            {
                if (ps.isPlaying)
                {
                    activeSystems++;
                    totalParticles += ps.main.maxParticles;
                    
                    if (ps.main.maxParticles > 1000)
                    {
                        highParticleCountSystems++;
                    }
                }
            }
            
            var analysis = new
            {
                totalSystems = allParticleSystems.Length,
                activeSystems = activeSystems,
                totalMaxParticles = totalParticles,
                highParticleCountSystems = highParticleCountSystems,
                averageParticlesPerSystem = activeSystems > 0 ? totalParticles / activeSystems : 0,
                performanceRating = GetPerformanceRating(totalParticles, activeSystems),
                recommendations = GetOptimizationRecommendations(totalParticles, activeSystems, highParticleCountSystems)
            };
            
            return JsonHelper.CreateSuccessResponse("VFX performance analysis completed", analysis);
        }
        
        private static string GetPerformanceRating(int totalParticles, int activeSystems)
        {
            if (totalParticles < 5000 && activeSystems < 20)
                return "Excellent";
            else if (totalParticles < 15000 && activeSystems < 50)
                return "Good";
            else if (totalParticles < 30000 && activeSystems < 100)
                return "Fair";
            else
                return "Poor - Optimization Needed";
        }
        
        private static string[] GetOptimizationRecommendations(int totalParticles, int activeSystems, int highParticleCountSystems)
        {
            List<string> recommendations = new List<string>();
            
            if (totalParticles > 20000)
                recommendations.Add("Consider reducing overall particle count");
            
            if (activeSystems > 75)
                recommendations.Add("Implement distance culling for particle systems");
            
            if (highParticleCountSystems > 5)
                recommendations.Add("Optimize high particle count systems (>1000 particles)");
            
            if (totalParticles > 15000)
                recommendations.Add("Implement LOD system for particle effects");
            
            if (recommendations.Count == 0)
                recommendations.Add("Performance is within acceptable limits");
            
            return recommendations.ToArray();
        }
        
        // Helper component for distance culling
        public class DistanceCuller : MonoBehaviour
        {
            public float cullingDistance = 50.0f;
            public new ParticleSystem particleSystem; // Fixed: Use new keyword to hide inherited member
            private Camera mainCamera;
            
            void Start()
            {
                mainCamera = Camera.main;
                if (particleSystem == null)
                    particleSystem = GetComponent<ParticleSystem>();
            }
            
            void Update()
            {
                if (mainCamera != null && particleSystem != null)
                {
                    float distance = Vector3.Distance(transform.position, mainCamera.transform.position);
                    
                    if (distance > cullingDistance)
                    {
                        if (particleSystem.isPlaying)
                            particleSystem.Stop();
                    }
                    else
                    {
                        if (!particleSystem.isPlaying)
                            particleSystem.Play();
                    }
                }
            }
        }
    }
}