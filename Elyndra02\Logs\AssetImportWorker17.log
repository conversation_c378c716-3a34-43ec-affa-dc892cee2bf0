Using pre-set license
Built from '6000.2/staging' branch; Version is '6000.2.0b6 (************) revision 4430099'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'pt' Physical Memory: 32452 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-01T19:34:37Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b6\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker17
-projectPath
C:/ultimo/Elyndra02
-logFile
Logs/AssetImportWorker17.log
-srvPort
63893
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/ultimo/Elyndra02
C:/ultimo/Elyndra02
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [10640]  Target information:

Player connection [10640]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 395064194 [EditorId] 395064194 [Version] 1048832 [Id] WindowsEditor(7,tkt) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10640]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 395064194 [EditorId] 395064194 [Version] 1048832 [Id] WindowsEditor(7,tkt) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10640] Host joined multi-casting on [***********:54997]...
Player connection [10640] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 393.53 ms, found 13 plugins.
Preloading 1 native plugins for Editor in 1.16 ms.
Initialize engine version: 6000.2.0b6 (************)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b6/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/ultimo/Elyndra02/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        Intel(R) Iris(R) Xe Graphics (ID=0xa7a1)
    Vendor:          Intel
    VRAM:            16226 MB
    App VRAM Budget: 15458 MB
    Driver:          32.0.101.6556
    Unified Memory Architecture
    Cache Coherent UMA
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b6/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b6/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b6/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56692
Begin MonoManager ReloadAssembly
