#!/usr/bin/env python3
"""
Script automatizado para testar todos os comandos MCP do Unity Server
"""
import asyncio
import json
import time
import traceback
from typing import Dict, List, Any, Optional
import subprocess
import sys
import os

class MCPTester:
    def __init__(self):
        self.results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': [],
            'test_results': {},
            'categories': {}
        }
        self.server_process = None
        
    def load_inventory(self) -> Dict[str, Any]:
        """Carrega o inventário de ferramentas MCP."""
        try:
            with open('mcp_tools_inventory.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print("❌ Arquivo mcp_tools_inventory.json não encontrado!")
            print("   Execute primeiro: python test_mcp_inventory.py")
            sys.exit(1)
    
    def start_mcp_server(self) -> bool:
        """Inicia o servidor MCP."""
        print("🚀 Iniciando servidor MCP...")
        try:
            # Verificar se o servidor já está rodando
            self.server_process = subprocess.Popen([
                sys.executable, "-m", "UnityMcpServer.src.server"
            ], cwd=".", stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Aguardar um pouco para o servidor inicializar
            time.sleep(3)
            
            if self.server_process.poll() is None:
                print("✅ Servidor MCP iniciado com sucesso!")
                return True
            else:
                stdout, stderr = self.server_process.communicate()
                print(f"❌ Falha ao iniciar servidor MCP:")
                print(f"   STDOUT: {stdout.decode()}")
                print(f"   STDERR: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao iniciar servidor MCP: {e}")
            return False
    
    def stop_mcp_server(self):
        """Para o servidor MCP."""
        if self.server_process:
            print("🛑 Parando servidor MCP...")
            self.server_process.terminate()
            self.server_process.wait()
            print("✅ Servidor MCP parado.")
    
    def test_basic_connectivity(self) -> bool:
        """Testa conectividade básica com o servidor MCP."""
        print("🔌 Testando conectividade básica...")
        
        # Aqui implementaríamos um teste básico de conectividade
        # Por enquanto, vamos simular
        try:
            # Simular teste de conectividade
            time.sleep(1)
            print("✅ Conectividade básica OK")
            return True
        except Exception as e:
            print(f"❌ Falha na conectividade: {e}")
            return False
    
    def test_command_syntax(self, tool_info: Dict[str, Any]) -> Dict[str, Any]:
        """Testa a sintaxe de um comando MCP."""
        tool_name = tool_info['name']
        file_name = tool_info['file']
        
        result = {
            'tool': tool_name,
            'file': file_name,
            'status': 'unknown',
            'error': None,
            'parameters_count': len(tool_info['parameters'])
        }
        
        try:
            # Aqui implementaríamos o teste real do comando
            # Por enquanto, vamos fazer uma verificação básica
            
            # Simular teste de sintaxe
            if tool_name and len(tool_name) > 0:
                result['status'] = 'syntax_ok'
                self.results['passed'] += 1
            else:
                result['status'] = 'syntax_error'
                result['error'] = 'Nome da ferramenta inválido'
                self.results['failed'] += 1
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.results['failed'] += 1
            self.results['errors'].append({
                'tool': tool_name,
                'file': file_name,
                'error': str(e),
                'traceback': traceback.format_exc()
            })
        
        return result
    
    def test_category(self, category_name: str, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Testa uma categoria de ferramentas."""
        print(f"\n📂 Testando categoria: {category_name} ({len(tools)} ferramentas)")
        
        category_results = {
            'total': len(tools),
            'passed': 0,
            'failed': 0,
            'tools': {}
        }
        
        for i, tool in enumerate(tools, 1):
            tool_name = tool['name']
            print(f"  🔧 [{i:2d}/{len(tools):2d}] Testando {tool_name}...", end=' ')
            
            result = self.test_command_syntax(tool)
            category_results['tools'][tool_name] = result
            
            if result['status'] in ['syntax_ok', 'passed']:
                category_results['passed'] += 1
                print("✅")
            else:
                category_results['failed'] += 1
                print(f"❌ ({result['error']})")
        
        self.results['categories'][category_name] = category_results
        return category_results
    
    def run_all_tests(self):
        """Executa todos os testes."""
        print("🧪 INICIANDO TESTES COMPLETOS DO MCP UNITY SERVER")
        print("=" * 60)
        
        # Carregar inventário
        inventory = self.load_inventory()
        self.results['total_tests'] = inventory['total_tools']
        
        print(f"📊 Total de ferramentas a testar: {inventory['total_tools']}")
        print(f"📁 Total de categorias: {len(inventory['categories'])}")
        
        # Iniciar servidor MCP
        if not self.start_mcp_server():
            print("❌ Não foi possível iniciar o servidor MCP. Abortando testes.")
            return
        
        try:
            # Testar conectividade básica
            if not self.test_basic_connectivity():
                print("❌ Falha na conectividade básica. Abortando testes.")
                return
            
            # Testar cada categoria
            for category_name, tools in inventory['categories'].items():
                self.test_category(category_name, tools)
            
        finally:
            # Parar servidor MCP
            self.stop_mcp_server()
        
        # Gerar relatório final
        self.generate_report()
    
    def generate_report(self):
        """Gera relatório final dos testes."""
        print("\n" + "=" * 60)
        print("📊 RELATÓRIO FINAL DOS TESTES")
        print("=" * 60)
        
        print(f"🧪 Total de testes: {self.results['total_tests']}")
        print(f"✅ Sucessos: {self.results['passed']}")
        print(f"❌ Falhas: {self.results['failed']}")
        
        success_rate = (self.results['passed'] / self.results['total_tests']) * 100
        print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
        
        # Salvar relatório detalhado
        with open('mcp_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Relatório detalhado salvo em: mcp_test_results.json")
        
        # Mostrar erros se houver
        if self.results['errors']:
            print(f"\n❌ ERROS ENCONTRADOS ({len(self.results['errors'])}):")
            for error in self.results['errors'][:5]:  # Mostrar apenas os primeiros 5
                print(f"  • {error['tool']} ({error['file']}): {error['error']}")
            
            if len(self.results['errors']) > 5:
                print(f"  ... e mais {len(self.results['errors']) - 5} erros")

def main():
    """Função principal."""
    tester = MCPTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
