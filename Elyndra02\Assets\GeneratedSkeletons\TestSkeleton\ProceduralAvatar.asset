%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!90 &9000000
Avatar:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: ProceduralAvatar
  m_AvatarSize: 10296
  m_Avatar:
    serializedVersion: 3
    m_AvatarSkeleton:
      data:
        m_Node:
        - m_ParentId: -1
          m_AxesId: -1
        - m_ParentId: 0
          m_AxesId: -1
        - m_ParentId: 1
          m_AxesId: -1
        - m_ParentId: 2
          m_AxesId: -1
        - m_ParentId: 3
          m_AxesId: -1
        - m_ParentId: 4
          m_AxesId: -1
        - m_ParentId: 3
          m_AxesId: -1
        - m_ParentId: 6
          m_AxesId: -1
        - m_ParentId: 7
          m_AxesId: -1
        - m_ParentId: 8
          m_AxesId: -1
        - m_ParentId: 9
          m_AxesId: -1
        - m_ParentId: 10
          m_AxesId: -1
        - m_ParentId: 11
          m_AxesId: -1
        - m_ParentId: 9
          m_AxesId: -1
        - m_ParentId: 13
          m_AxesId: -1
        - m_ParentId: 14
          m_AxesId: -1
        - m_ParentId: 9
          m_AxesId: -1
        - m_ParentId: 16
          m_AxesId: -1
        - m_ParentId: 17
          m_AxesId: -1
        - m_ParentId: 9
          m_AxesId: -1
        - m_ParentId: 19
          m_AxesId: -1
        - m_ParentId: 20
          m_AxesId: -1
        - m_ParentId: 9
          m_AxesId: -1
        - m_ParentId: 22
          m_AxesId: -1
        - m_ParentId: 23
          m_AxesId: -1
        - m_ParentId: 3
          m_AxesId: -1
        - m_ParentId: 25
          m_AxesId: -1
        - m_ParentId: 26
          m_AxesId: -1
        - m_ParentId: 27
          m_AxesId: -1
        - m_ParentId: 28
          m_AxesId: -1
        - m_ParentId: 29
          m_AxesId: -1
        - m_ParentId: 30
          m_AxesId: -1
        - m_ParentId: 28
          m_AxesId: -1
        - m_ParentId: 32
          m_AxesId: -1
        - m_ParentId: 33
          m_AxesId: -1
        - m_ParentId: 28
          m_AxesId: -1
        - m_ParentId: 35
          m_AxesId: -1
        - m_ParentId: 36
          m_AxesId: -1
        - m_ParentId: 28
          m_AxesId: -1
        - m_ParentId: 38
          m_AxesId: -1
        - m_ParentId: 39
          m_AxesId: -1
        - m_ParentId: 28
          m_AxesId: -1
        - m_ParentId: 41
          m_AxesId: -1
        - m_ParentId: 42
          m_AxesId: -1
        - m_ParentId: 1
          m_AxesId: -1
        - m_ParentId: 44
          m_AxesId: -1
        - m_ParentId: 45
          m_AxesId: -1
        - m_ParentId: 46
          m_AxesId: -1
        - m_ParentId: 1
          m_AxesId: -1
        - m_ParentId: 48
          m_AxesId: -1
        - m_ParentId: 49
          m_AxesId: -1
        - m_ParentId: 50
          m_AxesId: -1
        m_ID: 000000001106d1decec2218c2e9309f367bf90c51ca7ab863360061f6c52ed67a30fe8bf8c90ccb7ae542a197b03033296351f01faa47d402230f0b53b150fc29e5243e9f2130aa0c350ed2b88a831e25e0ebfc2ecaf3dbadf9aa2d6354ea1077ed2010e294de9910804fae1859d4ae11f7d50e8efa456aeb2b6aa20cb4217f0bb5401f7b3c4036d9e3688aa685f2fe8e201510efd6732faf6d29d23c8745ff1ef15d28d2997ced727cd75602cb374044603372fa33d041cb31041c541d06bb68a232a99e68389b823cc0b1acd6707da
        m_AxesArray: []
    m_AvatarSkeletonPose:
      data:
        m_X:
        - t: {x: 0, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.27, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.21599999, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.324, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.144, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.225, y: 0.21599999, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.225, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.35999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.324, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0324, y: -0.0216, z: -0.0324}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.027, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0216, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.1152, y: -0.0144, z: 0.0432}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0288, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.16199999, y: 0, z: 0.018}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.044999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.12959999, y: 0, z: -0.016199999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.040499996, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.032399997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0756, y: 0, z: -0.0378}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.031499997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.025199998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.225, y: 0.21599999, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.225, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.35999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.324, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0324, y: -0.0216, z: -0.0324}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.027, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0216, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.1152, y: -0.0144, z: 0.0432}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0288, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.16199999, y: 0, z: 0.018}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.044999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.12959999, y: 0, z: -0.016199999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.040499996, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.032399997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0756, y: 0, z: -0.0378}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.031499997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.025199998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.27, y: -0.089999996, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0, z: 0.21599999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.27, y: -0.089999996, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0, z: 0.21599999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
    m_DefaultPose:
      data:
        m_X:
        - t: {x: 0, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.27, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.21599999, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.324, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0.144, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.225, y: 0.21599999, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.225, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.35999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.324, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0324, y: -0.0216, z: -0.0324}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.027, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0216, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.1152, y: -0.0144, z: 0.0432}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0288, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.16199999, y: 0, z: 0.018}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.044999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.12959999, y: 0, z: -0.016199999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.040499996, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.032399997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.0756, y: 0, z: -0.0378}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.031499997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.025199998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.225, y: 0.21599999, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.225, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.35999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.324, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0324, y: -0.0216, z: -0.0324}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.027, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0216, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.1152, y: -0.0144, z: 0.0432}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0288, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.16199999, y: 0, z: 0.018}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.044999998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.036, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.12959999, y: 0, z: -0.016199999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.040499996, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.032399997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.0756, y: 0, z: -0.0378}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.031499997, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.025199998, y: 0, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: -0.27, y: -0.089999996, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0, z: 0.21599999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0.27, y: -0.089999996, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: -0.45, z: 0}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
        - t: {x: 0, y: 0, z: 0.21599999}
          q: {x: 0, y: 0, z: 0, w: 1}
          s: {x: 1, y: 1, z: 1}
    m_SkeletonNameIDArray: 000000001106d1decbc10e53b39c853a6d792380a259c107a860462d8eb1b32b1147d4c46cf375b6ae2d28cf5b5fffae01ee262cfadd7f96bd9619b88d99aaefe75095e9e7e6ac97181e6b0a2840eb36fa2074028ada95f4a69874d6d7f1bbdaee91a01ae54706f6c396f3f05c60941f304df975c5b7f840d149634ebc037df89147af19378085583074f13bad387a33cf10219ac48a6fbff4d4ef83f0b48b4be9854814ecf09b0cff0736d73205a4af554f3dc6cab95a29708898584d6a03c618687d1d879e1af22c36149b11d48f05
    m_Human:
      data:
        serializedVersion: 2
        m_RootX:
          t: {x: 0.0032727271, y: 0.1711637, z: 0.0010472728}
          q: {x: -0, y: -0, z: -0, w: 1}
          s: {x: 1, y: 1, z: 1}
        m_Skeleton:
          data:
            m_Node:
            - m_ParentId: -1
              m_AxesId: -1
            - m_ParentId: 0
              m_AxesId: 0
            - m_ParentId: 1
              m_AxesId: 1
            - m_ParentId: 2
              m_AxesId: 2
            - m_ParentId: 3
              m_AxesId: 3
            - m_ParentId: 4
              m_AxesId: 4
            - m_ParentId: 3
              m_AxesId: 5
            - m_ParentId: 6
              m_AxesId: 6
            - m_ParentId: 7
              m_AxesId: 7
            - m_ParentId: 8
              m_AxesId: 8
            - m_ParentId: 3
              m_AxesId: 9
            - m_ParentId: 10
              m_AxesId: 10
            - m_ParentId: 11
              m_AxesId: 11
            - m_ParentId: 12
              m_AxesId: 12
            - m_ParentId: 1
              m_AxesId: 13
            - m_ParentId: 14
              m_AxesId: 14
            - m_ParentId: 15
              m_AxesId: 15
            - m_ParentId: 16
              m_AxesId: 16
            - m_ParentId: 1
              m_AxesId: 17
            - m_ParentId: 18
              m_AxesId: 18
            - m_ParentId: 19
              m_AxesId: 19
            - m_ParentId: 20
              m_AxesId: 20
            m_ID: 000000001106d1decec2218c2e9309f367bf90c51ca7ab863360061f6c52ed67a30fe8bf8c90ccb7294de9910804fae1859d4ae11f7d50e84603372fa33d041cb31041c541d06bb68a232a99e68389b823cc0b1acd6707da
            m_AxesArray:
            - m_PreQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -0.6981317, y: -0.6981317, z: -0.6981317}
                m_Max: {x: 0.6981317, y: 0.6981317, z: 0.6981317}
              m_Length: 0.27
              m_Type: 1
            - m_PreQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -0.6981317, y: -0.6981317, z: -0.6981317}
                m_Max: {x: 0.6981317, y: 0.6981317, z: 0.6981317}
              m_Length: 0.21599999
              m_Type: 1
            - m_PreQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -0.6981317, y: -0.6981317, z: -0.6981317}
                m_Max: {x: 0.6981317, y: 0.6981317, z: 0.6981317}
              m_Length: 0.324
              m_Type: 1
            - m_PreQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -0.6981317, y: -0.6981317, z: -0.6981317}
                m_Max: {x: 0.6981317, y: 0.6981317, z: 0.6981317}
              m_Length: 0.144
              m_Type: 1
            - m_PreQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: -0.5, z: 0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -0.6981317, y: -0.6981317, z: -0.6981317}
                m_Max: {x: 0.6981317, y: 0.6981317, z: 0.6981317}
              m_Length: 0.144
              m_Type: 1
            - m_PreQ: {x: -0, y: -0, z: 1, w: 0}
              m_PostQ: {x: -0, y: -0, z: 1, w: 0}
              m_Sgn: {x: 1, y: 1, z: -1}
              m_Limit:
                m_Min: {x: 0, y: -0.2617994, z: -0.2617994}
                m_Max: {x: 0, y: 0.2617994, z: 0.5235988}
              m_Length: 0.225
              m_Type: 1
            - m_PreQ: {x: 0.24421024, y: -0, z: 0.91123223, w: -0.33168852}
              m_PostQ: {x: -0, y: -0, z: 1, w: 0}
              m_Sgn: {x: 1, y: 1, z: -1}
              m_Limit:
                m_Min: {x: -1.5707964, y: -1.7453293, z: -1.0471976}
                m_Max: {x: 1.5707964, y: 1.7453293, z: 1.7453293}
              m_Length: 0.35999995
              m_Type: 1
            - m_PreQ: {x: 0.45448783, y: 0.54170185, z: 0.54170185, w: -0.45448783}
              m_PostQ: {x: -0, y: 0.7071068, z: 0.7071068, w: 0}
              m_Sgn: {x: 1, y: 1, z: -1}
              m_Limit:
                m_Min: {x: -1.5707964, y: 0, z: -1.3962634}
                m_Max: {x: 1.5707964, y: 0, z: 1.3962634}
              m_Length: 0.324
              m_Type: 1
            - m_PreQ: {x: -0, y: -0, z: 1, w: 0}
              m_PostQ: {x: -0, y: -0, z: 1, w: 0}
              m_Sgn: {x: 1, y: 1, z: -1}
              m_Limit:
                m_Min: {x: 0, y: -0.6981317, z: -1.3962634}
                m_Max: {x: 0, y: 0.6981317, z: 1.3962634}
              m_Length: 0.081
              m_Type: 1
            - m_PreQ: {x: -0, y: -0, z: -0, w: 1}
              m_PostQ: {x: -0, y: -0, z: -0, w: 1}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: 0, y: -0.2617994, z: -0.2617994}
                m_Max: {x: 0, y: 0.2617994, z: 0.5235988}
              m_Length: 0.225
              m_Type: 1
            - m_PreQ: {x: -0, y: -0.24421024, z: -0.33168852, w: 0.91123223}
              m_PostQ: {x: -0, y: -0, z: -0, w: 1}
              m_Sgn: {x: -1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -1.5707964, y: -1.7453293, z: -1.0471976}
                m_Max: {x: 1.5707964, y: 1.7453293, z: 1.7453293}
              m_Length: 0.35999995
              m_Type: 1
            - m_PreQ: {x: -0.54170185, y: -0.45448783, z: -0.45448783, w: 0.54170185}
              m_PostQ: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
              m_Sgn: {x: -1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -1.5707964, y: 0, z: -1.3962634}
                m_Max: {x: 1.5707964, y: 0, z: 1.3962634}
              m_Length: 0.324
              m_Type: 1
            - m_PreQ: {x: -0, y: -0, z: -0, w: 1}
              m_PostQ: {x: -0, y: -0, z: -0, w: 1}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: 0, y: -0.6981317, z: -1.3962634}
                m_Max: {x: 0, y: 0.6981317, z: 1.3962634}
              m_Length: 0.081
              m_Type: 1
            - m_PreQ: {x: -0.6123892, y: 0.3535244, z: -0.6123892, w: 0.3535244}
              m_PostQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: -1.0471976, y: -1.0471976, z: -1.5707964}
                m_Max: {x: 1.0471976, y: 1.0471976, z: 0.87266463}
              m_Length: 0.44999996
              m_Type: 1
            - m_PreQ: {x: -0.061669607, y: 0.7044124, z: -0.061669607, w: 0.7044124}
              m_PostQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: -1}
              m_Limit:
                m_Min: {x: -1.5707964, y: 0, z: -1.3962634}
                m_Max: {x: 1.5707964, y: 0, z: 1.3962634}
              m_Length: 0.45
              m_Type: 1
            - m_PreQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: 0, y: -0.5235988, z: -0.87266463}
                m_Max: {x: 0, y: 0.5235988, z: 0.87266463}
              m_Length: 0.98999995
              m_Type: 1
            - m_PreQ: {x: 0.7071068, y: -0, z: 0.7071068, w: 0}
              m_PostQ: {x: 0.7071068, y: -0, z: 0.7071068, w: 0}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: 0, y: 0, z: -0.87266463}
                m_Max: {x: 0, y: 0, z: 0.87266463}
              m_Length: 0.053999998
              m_Type: 1
            - m_PreQ: {x: -0.6123892, y: 0.3535244, z: -0.6123892, w: 0.3535244}
              m_PostQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_Sgn: {x: -1, y: -1, z: 1}
              m_Limit:
                m_Min: {x: -1.0471976, y: -1.0471976, z: -1.5707964}
                m_Max: {x: 1.0471976, y: 1.0471976, z: 0.87266463}
              m_Length: 0.44999996
              m_Type: 1
            - m_PreQ: {x: -0.061669607, y: 0.7044124, z: -0.061669607, w: 0.7044124}
              m_PostQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_Sgn: {x: -1, y: 1, z: -1}
              m_Limit:
                m_Min: {x: -1.5707964, y: 0, z: -1.3962634}
                m_Max: {x: 1.5707964, y: 0, z: 1.3962634}
              m_Length: 0.45
              m_Type: 1
            - m_PreQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_PostQ: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
              m_Sgn: {x: 1, y: -1, z: 1}
              m_Limit:
                m_Min: {x: 0, y: -0.5235988, z: -0.87266463}
                m_Max: {x: 0, y: 0.5235988, z: 0.87266463}
              m_Length: 0.98999995
              m_Type: 1
            - m_PreQ: {x: 0.7071068, y: -0, z: 0.7071068, w: 0}
              m_PostQ: {x: 0.7071068, y: -0, z: 0.7071068, w: 0}
              m_Sgn: {x: 1, y: 1, z: 1}
              m_Limit:
                m_Min: {x: 0, y: 0, z: -0.87266463}
                m_Max: {x: 0, y: 0, z: 0.87266463}
              m_Length: 0.053999998
              m_Type: 1
        m_SkeletonPose:
          data:
            m_X:
            - t: {x: 0, y: 0, z: 0}
              q: {x: 0, y: 0, z: 0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0.27, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0.21599999, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0.324, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0.144, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: -0.225, y: 0.21600002, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: -0.225, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: -0.35999995, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: -0.324, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0.225, y: 0.21600002, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0.225, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0.35999995, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0.324, y: 0, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: -0.27, y: -0.089999996, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: -0.44999996, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: -0.45, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0, z: 0.21599999}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0.27, y: -0.089999996, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: -0.44999996, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: -0.45, z: 0}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
            - t: {x: 0, y: 0, z: 0.21599999}
              q: {x: -0, y: -0, z: -0, w: 1}
              s: {x: 1, y: 1, z: 1}
        m_LeftHand:
          data:
            m_HandBoneIndex: ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
        m_RightHand:
          data:
            m_HandBoneIndex: ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
        m_HumanBoneIndex: 010000000e000000120000000f0000001300000010000000140000000200000003000000ffffffff0400000005000000060000000a000000070000000b000000080000000c000000090000000d0000001100000015000000ffffffffffffffffffffffff
        m_HumanBoneMass:
        - 0.14545456
        - 0.12121213
        - 0.12121213
        - 0.048484854
        - 0.048484854
        - 0.009696971
        - 0.009696971
        - 0.030303033
        - 0.2909091
        - 0
        - 0.012121214
        - 0.048484854
        - 0.006060607
        - 0.006060607
        - 0.024242427
        - 0.024242427
        - 0.01818182
        - 0.01818182
        - 0.006060607
        - 0.006060607
        - 0.0024242427
        - 0.0024242427
        - 0
        - 0
        - 0
        m_Scale: 0.1711637
        m_ArmTwist: 0.5
        m_ForeArmTwist: 0.5
        m_UpperLegTwist: 0.5
        m_LegTwist: 0.5
        m_ArmStretch: 0.05
        m_LegStretch: 0.05
        m_FeetSpacing: 0
        m_HasLeftHand: 0
        m_HasRightHand: 0
        m_HasTDoF: 0
    m_HumanSkeletonIndexArray: 00000000010000000200000003000000040000000500000006000000070000000800000009000000190000001a0000001b0000001c0000002c0000002d0000002e0000002f00000030000000310000003200000033000000
    m_HumanSkeletonReverseIndexArray: 00000000010000000200000003000000040000000500000006000000070000000800000009000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0a0000000b0000000c0000000d000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0e0000000f000000100000001100000012000000130000001400000015000000
    m_RootMotionBoneIndex: -1
    m_RootMotionBoneX:
      t: {x: 0, y: 0, z: 0}
      q: {x: 0, y: 0, z: 0, w: 1}
      s: {x: 1, y: 1, z: 1}
    m_RootMotionSkeleton:
      data:
        m_Node: []
        m_ID: 
        m_AxesArray: []
    m_RootMotionSkeletonPose:
      data:
        m_X: []
    m_RootMotionSkeletonIndexArray: 
  m_TOS:
    4049564872: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightRingProximal/RightRingIntermediate
    3791258632: Hips/Spine/Chest/RightShoulder/RightUpperArm
    240189922: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightMiddleProximal/RightMiddleIntermediate
    422204590: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftThumbProximal
    3620640553: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightLittleProximal
    4028056267: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightThumbProximal/RightThumbIntermediate/RightThumbDistal
    3096019942: Hips/RightUpperLeg/RightLowerLeg
    2351022798: Hips/Spine
    3600980703: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftLittleProximal
    74756908: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightLittleProximal/RightLittleIntermediate/RightLittleDistal
    3255768379: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftIndexProximal/LeftIndexIntermediate/LeftIndexDistal
    18822550: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftThumbProximal/LeftThumbIntermediate/LeftThumbDistal
    2447985961: Hips/Spine/Chest/RightShoulder
    235000446: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftLittleProximal/LeftLittleIntermediate/LeftLittleDistal
    0: 
    2379355631: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightRingProximal/RightRingIntermediate/RightRingDistal
    4144059579: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightIndexProximal
    2861053598: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightIndexProximal/RightIndexIntermediate/RightIndexDistal
    548058802: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightThumbProximal/RightThumbIntermediate
    839058299: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftThumbProximal/LeftThumbIntermediate
    1828963507: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightIndexProximal/RightIndexIntermediate
    3309375667: Hips/LeftUpperLeg/LeftLowerLeg/LeftFoot
    3083636876: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand
    3124604908: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftRingProximal/LeftRingIntermediate/LeftRingDistal
    3219656611: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm
    4197607421: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightMiddleProximal/RightMiddleIntermediate/RightMiddleDistal
    1618332967: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightLittleProximal/RightLittleIntermediate
    3267300958: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftRingProximal/LeftRingIntermediate
    3895418728: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightMiddleProximal
    3913503390: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftMiddleProximal
    3794905224: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftRingProximal
    436980771: Hips/RightUpperLeg/RightLowerLeg/RightFoot
    736972995: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftMiddleProximal/LeftMiddleIntermediate/LeftMiddleDistal
    2924913903: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightThumbProximal
    2569675658: Hips/RightUpperLeg
    2259396380: Hips/Spine/Chest/Neck/Head
    2685015026: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftMiddleProximal/LeftMiddleIntermediate
    1081976058: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftIndexProximal
    3052417058: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftIndexProximal/LeftIndexIntermediate
    597545718: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand/RightRingProximal
    792134470: Hips/LeftUpperLeg
    520511539: Hips/Spine/Chest/LeftShoulder
    1743606380: Hips/Spine/Chest/LeftShoulder/LeftUpperArm
    3314597735: Hips/Spine/Chest/Neck
    470039971: Hips/LeftUpperLeg/LeftLowerLeg
    3779763589: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm
    3657918413: Hips/RightUpperLeg/RightLowerLeg/RightFoot/RightToes
    3060518977: Hips/LeftUpperLeg/LeftLowerLeg/LeftFoot/LeftToes
    128011829: Hips/Spine/Chest/LeftShoulder/LeftUpperArm/LeftLowerArm/LeftHand/LeftLittleProximal/LeftLittleIntermediate
    3897589023: Hips/Spine/Chest/RightShoulder/RightUpperArm/RightLowerArm/RightHand
    3738240529: Hips
    4077490990: Hips/Spine/Chest
  m_HumanDescription:
    serializedVersion: 3
    m_Human:
    - m_BoneName: Hips
      m_HumanName: Hips
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: Spine
      m_HumanName: Spine
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: Chest
      m_HumanName: Chest
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: Neck
      m_HumanName: Neck
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: Head
      m_HumanName: Head
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftShoulder
      m_HumanName: LeftShoulder
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftUpperArm
      m_HumanName: LeftUpperArm
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftLowerArm
      m_HumanName: LeftLowerArm
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftHand
      m_HumanName: LeftHand
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightShoulder
      m_HumanName: RightShoulder
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightUpperArm
      m_HumanName: RightUpperArm
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightLowerArm
      m_HumanName: RightLowerArm
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightHand
      m_HumanName: RightHand
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftUpperLeg
      m_HumanName: LeftUpperLeg
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftLowerLeg
      m_HumanName: LeftLowerLeg
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftFoot
      m_HumanName: LeftFoot
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: LeftToes
      m_HumanName: LeftToes
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightUpperLeg
      m_HumanName: RightUpperLeg
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightLowerLeg
      m_HumanName: RightLowerLeg
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightFoot
      m_HumanName: RightFoot
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    - m_BoneName: RightToes
      m_HumanName: RightToes
      m_Limit:
        m_Min: {x: 0, y: 0, z: 0}
        m_Max: {x: 0, y: 0, z: 0}
        m_Value: {x: 0, y: 0, z: 0}
        m_Length: 0
        m_Modified: 0
    m_Skeleton:
    - m_Name: Hips
      m_ParentName: 
      m_Position: {x: 0, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: Spine
      m_ParentName: 
      m_Position: {x: 0, y: 0.27, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: Chest
      m_ParentName: 
      m_Position: {x: 0, y: 0.21599999, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: Neck
      m_ParentName: 
      m_Position: {x: 0, y: 0.324, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: Head
      m_ParentName: 
      m_Position: {x: 0, y: 0.144, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftShoulder
      m_ParentName: 
      m_Position: {x: -0.225, y: 0.21599999, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftUpperArm
      m_ParentName: 
      m_Position: {x: -0.225, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftLowerArm
      m_ParentName: 
      m_Position: {x: -0.35999998, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftHand
      m_ParentName: 
      m_Position: {x: -0.324, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftThumbProximal
      m_ParentName: 
      m_Position: {x: -0.0324, y: -0.0216, z: -0.0324}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftThumbIntermediate
      m_ParentName: 
      m_Position: {x: -0.027, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftThumbDistal
      m_ParentName: 
      m_Position: {x: -0.0216, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftIndexProximal
      m_ParentName: 
      m_Position: {x: -0.1152, y: -0.0144, z: 0.0432}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftIndexIntermediate
      m_ParentName: 
      m_Position: {x: -0.036, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftIndexDistal
      m_ParentName: 
      m_Position: {x: -0.0288, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftMiddleProximal
      m_ParentName: 
      m_Position: {x: -0.16199999, y: 0, z: 0.018}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftMiddleIntermediate
      m_ParentName: 
      m_Position: {x: -0.044999998, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftMiddleDistal
      m_ParentName: 
      m_Position: {x: -0.036, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftRingProximal
      m_ParentName: 
      m_Position: {x: -0.12959999, y: 0, z: -0.016199999}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftRingIntermediate
      m_ParentName: 
      m_Position: {x: -0.040499996, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftRingDistal
      m_ParentName: 
      m_Position: {x: -0.032399997, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftLittleProximal
      m_ParentName: 
      m_Position: {x: -0.0756, y: 0, z: -0.0378}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftLittleIntermediate
      m_ParentName: 
      m_Position: {x: -0.031499997, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftLittleDistal
      m_ParentName: 
      m_Position: {x: -0.025199998, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightShoulder
      m_ParentName: 
      m_Position: {x: 0.225, y: 0.21599999, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightUpperArm
      m_ParentName: 
      m_Position: {x: 0.225, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightLowerArm
      m_ParentName: 
      m_Position: {x: 0.35999998, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightHand
      m_ParentName: 
      m_Position: {x: 0.324, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightThumbProximal
      m_ParentName: 
      m_Position: {x: 0.0324, y: -0.0216, z: -0.0324}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightThumbIntermediate
      m_ParentName: 
      m_Position: {x: 0.027, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightThumbDistal
      m_ParentName: 
      m_Position: {x: 0.0216, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightIndexProximal
      m_ParentName: 
      m_Position: {x: 0.1152, y: -0.0144, z: 0.0432}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightIndexIntermediate
      m_ParentName: 
      m_Position: {x: 0.036, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightIndexDistal
      m_ParentName: 
      m_Position: {x: 0.0288, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightMiddleProximal
      m_ParentName: 
      m_Position: {x: 0.16199999, y: 0, z: 0.018}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightMiddleIntermediate
      m_ParentName: 
      m_Position: {x: 0.044999998, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightMiddleDistal
      m_ParentName: 
      m_Position: {x: 0.036, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightRingProximal
      m_ParentName: 
      m_Position: {x: 0.12959999, y: 0, z: -0.016199999}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightRingIntermediate
      m_ParentName: 
      m_Position: {x: 0.040499996, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightRingDistal
      m_ParentName: 
      m_Position: {x: 0.032399997, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightLittleProximal
      m_ParentName: 
      m_Position: {x: 0.0756, y: 0, z: -0.0378}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightLittleIntermediate
      m_ParentName: 
      m_Position: {x: 0.031499997, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightLittleDistal
      m_ParentName: 
      m_Position: {x: 0.025199998, y: 0, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftUpperLeg
      m_ParentName: 
      m_Position: {x: -0.27, y: -0.089999996, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftLowerLeg
      m_ParentName: 
      m_Position: {x: 0, y: -0.45, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftFoot
      m_ParentName: 
      m_Position: {x: 0, y: -0.45, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: LeftToes
      m_ParentName: 
      m_Position: {x: 0, y: 0, z: 0.21599999}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightUpperLeg
      m_ParentName: 
      m_Position: {x: 0.27, y: -0.089999996, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightLowerLeg
      m_ParentName: 
      m_Position: {x: 0, y: -0.45, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightFoot
      m_ParentName: 
      m_Position: {x: 0, y: -0.45, z: 0}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    - m_Name: RightToes
      m_ParentName: 
      m_Position: {x: 0, y: 0, z: 0.21599999}
      m_Rotation: {x: 0, y: 0, z: 0, w: 1}
      m_Scale: {x: 1, y: 1, z: 1}
    m_ArmTwist: 0.5
    m_ForeArmTwist: 0.5
    m_UpperLegTwist: 0.5
    m_LegTwist: 0.5
    m_ArmStretch: 0.05
    m_LegStretch: 0.05
    m_FeetSpacing: 0
    m_GlobalScale: 0
    m_RootMotionBoneName: 
    m_HasTranslationDoF: 0
    m_HasExtraRoot: 0
    m_SkeletonHasParents: 0
